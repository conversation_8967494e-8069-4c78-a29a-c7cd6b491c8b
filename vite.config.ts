import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// Tüm environment değişkenlerini .env dosyasından çekmek için
import dotenv from 'dotenv';
dotenv.config();

export default defineConfig({
  plugins: [react()],
  server: {
    port: 5174,
    proxy: {
      '/api': {
        target: 'http://localhost:8787',
        changeOrigin: true,
      },
    },
  },
  define: {
    global: 'globalThis',
  },
  optimizeDeps: {
    include: ['buffer', 'process'],
  },
  ssr: {
    noExternal: [
      '@solana/wallet-adapter-wallets',
      '@solana/wallet-adapter-walletconnect',
      '@walletconnect/client',
      '@walletconnect/web3-provider',
    ],
  },
});
