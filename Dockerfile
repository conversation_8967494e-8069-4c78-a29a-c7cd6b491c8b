# Stage 1: Base image with Node.js
# Use an official Node.js image. Alpine Linux is a good choice for size.
FROM node:20-alpine AS base

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json (or npm-shrinkwrap.json)
COPY package*.json ./

# Stage 2: Install dependencies
# This layer is cached to speed up builds if dependencies haven't changed
FROM base AS dependencies
RUN npm install --omit=dev

# Stage 3: Build the application
# Copy all source code
FROM dependencies AS build
COPY . .

# In case you have a build step for your backend (e.g., TypeScript compilation),
# you would run it here. For this pure JS server, it's not needed.

# Stage 4: Production image
# Create a new, smaller image for production
FROM base AS production

# Copy only the necessary files from the previous stages
COPY --from=dependencies /app/node_modules ./node_modules
COPY --from=build /app .

# Expose the port the app runs on
EXPOSE 8787

# Define the command to run your app
CMD ["node", "server.js"] 