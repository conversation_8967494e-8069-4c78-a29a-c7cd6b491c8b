#!/bin/bash

# Agora Marketplace Database Setup Script
echo "Setting up Agora Marketplace database..."

# Check if PostgreSQL is running
if ! command -v psql &> /dev/null; then
    # Try to find psql in common locations
    if [ -f "/opt/homebrew/bin/psql" ]; then
        export PATH="/opt/homebrew/bin:$PATH"
    elif [ -f "/usr/local/bin/psql" ]; then
        export PATH="/usr/local/bin:$PATH"
    else
        echo "Error: PostgreSQL is not installed or not in PATH"
        echo "Please install PostgreSQL first:"
        echo "  macOS: brew install postgresql"
        echo "  Ubuntu: sudo apt-get install postgresql postgresql-contrib"
        exit 1
    fi
fi

# Check if PostgreSQL service is running
if ! pg_isready -q; then
    echo "Starting PostgreSQL service..."
    if command -v brew &> /dev/null; then
        brew services start postgresql@14
    elif command -v systemctl &> /dev/null; then
        sudo systemctl start postgresql
    else
        echo "Please start PostgreSQL manually"
        exit 1
    fi

    # Wait for PostgreSQL to start
    sleep 5
fi

echo "PostgreSQL is running"

# Create database and user
echo "Creating database and user..."

# Connect as superuser (usually postgres or your system user)
psql postgres << EOF
-- Create user if not exists
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'agora_user') THEN
        CREATE USER agora_user WITH PASSWORD 'agora_password';
    END IF;
END
\$\$;

-- Create database if not exists
SELECT 'CREATE DATABASE agora_marketplace OWNER agora_user'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'agora_marketplace')\gexec

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE agora_marketplace TO agora_user;
ALTER USER agora_user CREATEDB;
EOF

if [ $? -eq 0 ]; then
    echo "Database and user created successfully"
else
    echo "Error creating database or user"
    exit 1
fi

# Run schema setup
echo "Setting up database schema..."
psql -U agora_user -d agora_marketplace -f database/schema.sql

if [ $? -eq 0 ]; then
    echo "Schema created successfully"
else
    echo "Error creating schema"
    exit 1
fi

# Run data setup
echo "Inserting sample data..."
psql -U agora_user -d agora_marketplace -f database/setup.sql

if [ $? -eq 0 ]; then
    echo "Sample data inserted successfully"
else
    echo "Error inserting sample data"
    exit 1
fi

echo "Database setup completed successfully!"
echo "You can now start the server with: npm run server"
