# Server Configuration
NODE_ENV=development
PORT=8787
SESSION_SECRET=your-secret-session-key-change-in-production
JWT_SECRET=your-jwt-secret-key-change-in-production

# Frontend URLs
FRONTEND_URL=http://localhost:5174
ADMIN_URL=http://localhost:5174

# Database Configuration (PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=agora_marketplace
DB_USER=agora_user
DB_PASSWORD=agora_password
DATABASE_URL=postgresql://agora_user:agora_password@localhost:5432/agora_marketplace

# Supabase Configuration (Optional - for additional features)
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-anon-key

# AI API Keys
GEMINI_API_KEY=your-gemini-api-key
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key
GROK_API_KEY=your-grok-api-key
XAI_API_KEY=your-xai-api-key

# Google Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_PROJECT_ID=your-google-project-id

# Additional Anthropic Keys (for load balancing)
VITE_ANTHROPIC_KEY_1=your-anthropic-key-1
VITE_ANTHROPIC_KEY_2=your-anthropic-key-2
VITE_ANTHROPIC_KEY_3=your-anthropic-key-3

# Hugging Face
HUGGINGFACE_API_KEY=your-huggingface-api-key

# Solana Configuration
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_NETWORK=mainnet-beta

# Payment Configuration
DGPU_TOKEN_MINT=your-dgpu-token-mint-address
PAYMENT_WALLET=your-payment-wallet-address
