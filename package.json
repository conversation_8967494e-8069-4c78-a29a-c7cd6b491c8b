{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "start": "concurrently \"npm:dev\" \"npm:server\"", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "server": "node server.js", "db:create": "createdb agora_marketplace", "db:setup": "psql -d agora_marketplace -f database/setup.sql", "db:schema": "psql -d agora_marketplace -f database/schema.sql", "db:reset": "dropdb --if-exists agora_marketplace && createdb agora_marketplace && npm run db:schema", "db:migrate": "npm run db:schema"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.13", "@google-cloud/local-auth": "^3.0.1", "@huggingface/inference": "^2.6.4", "@solana-mobile/wallet-adapter-mobile": "^2.1.5", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-react": "^0.15.16", "@solana/wallet-adapter-react-ui": "^0.9.35", "@solana/wallet-adapter-wallets": "^0.19.32", "@solana/web3.js": "^1.91.1", "@supabase/supabase-js": "^2.50.0", "@uiw/react-markdown-preview": "^5.0.7", "abort-controller": "^3.0.0", "abso": "^1.0.0", "abso-ai": "^0.0.9", "ai": "^4.3.11", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "bs58": "^6.0.0", "buffer": "^6.0.3", "combined-stream": "^1.0.8", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^3.3.1", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "extend": "^3.0.2", "form-data": "^4.0.2", "google-auth-library": "^9.15.1", "googleapis": "^150.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.344.0", "marked": "^15.0.8", "morgan": "^1.10.0", "pg": "^8.16.0", "process": "^0.11.10", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-markdown": "^9.0.1", "react-router-dom": "^6.22.3", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.15.3", "remark-gfm": "^4.0.0", "slugify": "^1.6.6", "supabase": "^2.24.3", "tweetnacl": "^1.0.3", "uuid": "^11.1.0", "zod": "^3.25.56", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/express": "^5.0.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.11", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^8.2.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.18"}}