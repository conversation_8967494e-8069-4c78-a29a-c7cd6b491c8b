const { pool } = require('../../lib/database');

// Request logger middleware
const requestLogger = (req, res, next) => {
  const start = Date.now();
  const timestamp = new Date().toISOString();

  console.log(`[${timestamp}] ${req.method} ${req.url} - ${req.ip}`);

  res.on('finish', () => {
    const duration = Date.now() - start;
    const statusColor = res.statusCode >= 400 ? '\x1b[31m' : '\x1b[32m'; // Red for errors, green for success
    const resetColor = '\x1b[0m';

    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url} - ${statusColor}${res.statusCode}${resetColor} - ${duration}ms`);
  });

  next();
};

// Error logger middleware
const errorLogger = (error, req) => {
  const timestamp = new Date().toISOString();
  console.error(`[${timestamp}] ERROR ${req.method} ${req.url}:`, error.message);
  if (process.env.NODE_ENV === 'development') {
    console.error(error.stack);
  }
};

// A simple admin authorization middleware
function authorize(requiredRole) {
  return (req, res, next) => {
    const userRole = req.headers['x-user-role'];
    if (userRole !== requiredRole) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    next();
  };
}

const TOOL_MAP = {
  '/api/gemini':   'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  '/api/deepseek': 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee',
  '/api/grok2':    '99999999-8888-7777-6666-555555555555',
  '/api/claude':   '77777777-6666-5555-4444-333333333333',
  '/api/llm':      '22222222-3333-4444-5555-666666666666',
  '/api/hf-image': '33333333-4444-5555-6666-777777777777'
};

function reportLogger(req, res, next) {
  if (req.method !== 'POST') return next();
  const start = Date.now();

  res.on('finish', async () => {
    const duration = Date.now() - start;
    const userId = req.headers['x-user-id'] || 'anonymous-user';
    const toolId = TOOL_MAP[req.path] || null;
    const success = res.statusCode < 400;
    const errorMsg = success ? null : (res.locals.error || res.statusMessage);

    try {
      // Create tool_reports table if using PostgreSQL logging
      await pool.query(`
        INSERT INTO tool_reports (tool_id, user_id, input_payload, output_payload, duration_ms, success, error_msg, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
      `, [toolId, userId, JSON.stringify(req.body), JSON.stringify(res.locals.output || {}), duration, success, errorMsg]);
    } catch (err) {
      console.error('[reportLogger] Database insert error:', err);
    }
  });

  next();
}

function usageLogger(req, res, next) {
  if (req.method !== 'POST') return next();

  const start = Date.now();

  res.on('finish', async () => {
    const duration = Date.now() - start;
    const userId = req.headers['x-user-id'] || null;
    const toolId = TOOL_MAP[req.path] || null;

    if (!userId || !toolId) return;

    try {
      // Log the specific usage event
      await pool.query(`
        INSERT INTO usage_logs (user_id, tool_id, invoked_at, params, duration_ms, status, error_code, source, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
      `, [userId, toolId, new Date(start).toISOString(), JSON.stringify(req.body), duration, res.statusCode < 400 ? 'success' : 'error', res.statusCode < 400 ? null : `HTTP_${res.statusCode}`, 'api']);

      // Update the user's interaction history
      await pool.query(`
        INSERT INTO user_agent_history (user_id, agent_id, total_interactions, total_duration_ms, last_interaction_at)
        VALUES ($1, $2, 1, $3, CURRENT_TIMESTAMP)
        ON CONFLICT (user_id, agent_id) 
        DO UPDATE SET
          total_interactions = user_agent_history.total_interactions + 1,
          total_duration_ms = user_agent_history.total_duration_ms + $3,
          last_interaction_at = CURRENT_TIMESTAMP
      `, [userId, toolId, duration]);

    } catch (err) {
      console.error('[usageLogger] Database insert error:', err);
    }
  });

  next();
}

module.exports = {
  authorize,
  reportLogger,
  usageLogger,
  requestLogger,
  errorLogger
};