const { pool } = require('../../lib/database');

// Usage tracking middleware for AI agent interactions
const trackUsage = (usageType = 'chat') => {
  return async (req, res, next) => {
    const start = Date.now();
    
    // Store original res.json to intercept response
    const originalJson = res.json;
    
    res.json = function(data) {
      // Call original json method
      originalJson.call(this, data);
      
      // Track usage asynchronously
      trackUsageAsync(req, res, start, usageType, data);
    };
    
    next();
  };
};

async function trackUsageAsync(req, res, start, usageType, responseData) {
  try {
    const duration = Date.now() - start;
    const userId = req.user?.id;
    const agentId = req.body?.agent_id || req.query?.agent_id;
    
    if (!userId) return; // Skip tracking for unauthenticated requests
    
    // Estimate tokens used (rough estimation)
    let tokensUsed = 0;
    if (req.body?.prompt || req.body?.content) {
      const inputText = req.body.prompt || req.body.content || '';
      tokensUsed = Math.ceil(inputText.length / 4); // Rough token estimation
    }
    
    if (responseData && typeof responseData === 'object') {
      const responseText = JSON.stringify(responseData);
      tokensUsed += Math.ceil(responseText.length / 4);
    }
    
    // Calculate cost based on agent pricing
    let cost = 0;
    if (agentId) {
      const agentResult = await pool.query(
        'SELECT price FROM agents WHERE id = $1',
        [agentId]
      );
      
      if (agentResult.rows.length > 0) {
        const agentPrice = parseFloat(agentResult.rows[0].price);
        cost = agentPrice * (tokensUsed / 1000); // Price per 1K tokens
      }
    }
    
    // Record usage
    await pool.query(
      `INSERT INTO agent_usage (
        user_id, agent_id, usage_type, tokens_used, cost, 
        session_id, metadata, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)`,
      [
        userId,
        agentId,
        usageType,
        tokensUsed,
        cost,
        req.sessionID || null,
        JSON.stringify({
          duration_ms: duration,
          status_code: res.statusCode,
          endpoint: req.path,
          method: req.method,
          user_agent: req.get('User-Agent'),
          ip: req.ip
        })
      ]
    );
    
    // Check if user has sufficient credits
    if (cost > 0 && agentId) {
      const creditsResult = await pool.query(
        'SELECT COALESCE(SUM(credits), 0) as total_credits FROM user_credits WHERE user_id = $1 AND agent_id = $2',
        [userId, agentId]
      );
      
      const totalCredits = parseFloat(creditsResult.rows[0].total_credits);
      
      if (totalCredits >= cost) {
        // Deduct credits
        await pool.query(
          `INSERT INTO user_credits (user_id, agent_id, credits, source, description, created_at)
           VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)`,
          [userId, agentId, -cost, 'usage', `${usageType} usage - ${tokensUsed} tokens`]
        );
      } else {
        // Log insufficient credits (but don't block the request)
        console.warn(`User ${userId} has insufficient credits for agent ${agentId}. Required: ${cost}, Available: ${totalCredits}`);
      }
    }
    
    // Update user agent history
    await pool.query(
      `INSERT INTO user_agent_history (user_id, agent_id, total_interactions, total_tokens, total_duration_ms, last_interaction_at)
       VALUES ($1, $2, 1, $3, $4, CURRENT_TIMESTAMP)
       ON CONFLICT (user_id, agent_id) 
       DO UPDATE SET
         total_interactions = user_agent_history.total_interactions + 1,
         total_tokens = user_agent_history.total_tokens + $3,
         total_duration_ms = user_agent_history.total_duration_ms + $4,
         last_interaction_at = CURRENT_TIMESTAMP`,
      [userId, agentId, tokensUsed, duration]
    );
    
  } catch (error) {
    console.error('Usage tracking error:', error);
    // Don't throw error to avoid breaking the main request
  }
}

// Middleware to check user credits before expensive operations
const checkCredits = (minimumCredits = 0.001) => {
  return async (req, res, next) => {
    try {
      const userId = req.user?.id;
      const agentId = req.body?.agent_id || req.query?.agent_id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_REQUIRED',
            message: 'Authentication required'
          }
        });
      }
      
      if (!agentId) {
        return next(); // Skip credit check if no agent specified
      }
      
      // Check user credits for this agent
      const creditsResult = await pool.query(
        'SELECT COALESCE(SUM(credits), 0) as total_credits FROM user_credits WHERE user_id = $1 AND agent_id = $2',
        [userId, agentId]
      );
      
      const totalCredits = parseFloat(creditsResult.rows[0].total_credits);
      
      if (totalCredits < minimumCredits) {
        return res.status(402).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_CREDITS',
            message: 'Insufficient credits for this operation',
            data: {
              required: minimumCredits,
              available: totalCredits,
              agent_id: agentId
            }
          }
        });
      }
      
      // Add credits info to request for use in handlers
      req.userCredits = totalCredits;
      next();
      
    } catch (error) {
      console.error('Credit check error:', error);
      next(error);
    }
  };
};

// Get usage statistics for a user
const getUserUsageStats = async (userId, timeframe = '30 days') => {
  try {
    const query = `
      SELECT 
        a.name as agent_name,
        a.slug as agent_slug,
        COUNT(*) as total_interactions,
        SUM(au.tokens_used) as total_tokens,
        SUM(au.cost) as total_cost,
        AVG(CAST(au.metadata->>'duration_ms' AS INTEGER)) as avg_duration_ms,
        MAX(au.created_at) as last_used
      FROM agent_usage au
      LEFT JOIN agents a ON au.agent_id = a.id
      WHERE au.user_id = $1 
        AND au.created_at >= NOW() - INTERVAL '${timeframe}'
      GROUP BY a.id, a.name, a.slug
      ORDER BY total_cost DESC
    `;
    
    const result = await pool.query(query, [userId]);
    return result.rows;
    
  } catch (error) {
    console.error('Error getting usage stats:', error);
    return [];
  }
};

// Get system-wide usage statistics (admin only)
const getSystemUsageStats = async (timeframe = '30 days') => {
  try {
    const query = `
      SELECT 
        a.name as agent_name,
        a.slug as agent_slug,
        COUNT(DISTINCT au.user_id) as unique_users,
        COUNT(*) as total_interactions,
        SUM(au.tokens_used) as total_tokens,
        SUM(au.cost) as total_revenue,
        AVG(CAST(au.metadata->>'duration_ms' AS INTEGER)) as avg_duration_ms
      FROM agent_usage au
      LEFT JOIN agents a ON au.agent_id = a.id
      WHERE au.created_at >= NOW() - INTERVAL '${timeframe}'
      GROUP BY a.id, a.name, a.slug
      ORDER BY total_revenue DESC
    `;
    
    const result = await pool.query(query);
    return result.rows;
    
  } catch (error) {
    console.error('Error getting system usage stats:', error);
    return [];
  }
};

module.exports = {
  trackUsage,
  checkCredits,
  getUserUsageStats,
  getSystemUsageStats
};
