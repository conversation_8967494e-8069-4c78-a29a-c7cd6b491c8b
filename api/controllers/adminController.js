const { pool } = require('../../lib/database');
const { getSystemUsageStats } = require('../middleware/usageTracker');

// Get dashboard statistics
exports.getDashboardStats = async (req, res, next) => {
  try {
    // Get basic counts
    const [usersResult, agentsResult, paymentsResult, usageResult] = await Promise.all([
      pool.query('SELECT COUNT(*) as count FROM users'),
      pool.query('SELECT COUNT(*) as count FROM agents WHERE status = $1', ['active']),
      pool.query('SELECT COUNT(*) as count, SUM(amount) as total_revenue FROM payments WHERE status = $1', ['confirmed']),
      pool.query('SELECT COUNT(*) as count FROM agent_usage WHERE created_at >= NOW() - INTERVAL \'24 hours\'')
    ]);

    // Get recent activity
    const recentActivity = await pool.query(`
      SELECT 
        'user_registration' as type,
        u.email,
        up.username,
        u.created_at
      FROM users u
      LEFT JOIN user_profiles up ON u.id = up.user_id
      WHERE u.created_at >= NOW() - INTERVAL '7 days'
      
      UNION ALL
      
      SELECT 
        'agent_creation' as type,
        a.name as email,
        a.creator_username as username,
        a.created_at
      FROM agents a
      WHERE a.created_at >= NOW() - INTERVAL '7 days'
      
      UNION ALL
      
      SELECT 
        'payment' as type,
        CAST(p.amount AS TEXT) as email,
        up.username,
        p.created_at
      FROM payments p
      LEFT JOIN user_profiles up ON p.user_id = up.user_id
      WHERE p.created_at >= NOW() - INTERVAL '7 days' AND p.status = 'confirmed'
      
      ORDER BY created_at DESC
      LIMIT 20
    `);

    // Get top agents by usage
    const topAgents = await pool.query(`
      SELECT 
        a.name,
        a.slug,
        COUNT(au.id) as usage_count,
        SUM(au.cost) as total_revenue,
        COUNT(DISTINCT au.user_id) as unique_users
      FROM agents a
      LEFT JOIN agent_usage au ON a.id = au.agent_id
      WHERE au.created_at >= NOW() - INTERVAL '30 days'
      GROUP BY a.id, a.name, a.slug
      ORDER BY usage_count DESC
      LIMIT 10
    `);

    // Get revenue over time (last 30 days)
    const revenueOverTime = await pool.query(`
      SELECT 
        DATE(created_at) as date,
        SUM(amount) as daily_revenue,
        COUNT(*) as daily_transactions
      FROM payments
      WHERE status = 'confirmed' 
        AND created_at >= NOW() - INTERVAL '30 days'
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `);

    res.json({
      success: true,
      data: {
        stats: {
          total_users: parseInt(usersResult.rows[0].count),
          active_agents: parseInt(agentsResult.rows[0].count),
          total_payments: parseInt(paymentsResult.rows[0].count),
          total_revenue: parseFloat(paymentsResult.rows[0].total_revenue || 0),
          daily_usage: parseInt(usageResult.rows[0].count)
        },
        recent_activity: recentActivity.rows,
        top_agents: topAgents.rows,
        revenue_over_time: revenueOverTime.rows
      }
    });

  } catch (error) {
    next(error);
  }
};

// Get all users with pagination
exports.getUsers = async (req, res, next) => {
  try {
    const { page = 1, limit = 20, search = '', status = '' } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramCount = 0;

    if (search) {
      paramCount++;
      whereClause += ` AND (u.email ILIKE $${paramCount} OR up.username ILIKE $${paramCount})`;
      queryParams.push(`%${search}%`);
    }

    if (status) {
      paramCount++;
      whereClause += ` AND u.role = $${paramCount}`;
      queryParams.push(status);
    }

    const query = `
      SELECT 
        u.*,
        up.username,
        up.avatar_url,
        up.wallet_address,
        up.wallet_connected_at,
        COALESCE(SUM(uc.credits), 0) as total_credits,
        COUNT(DISTINCT au.id) as total_usage
      FROM users u
      LEFT JOIN user_profiles up ON u.id = up.user_id
      LEFT JOIN user_credits uc ON u.id = uc.user_id
      LEFT JOIN agent_usage au ON u.id = au.user_id
      ${whereClause}
      GROUP BY u.id, up.username, up.avatar_url, up.wallet_address, up.wallet_connected_at
      ORDER BY u.created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    queryParams.push(limit, offset);
    const result = await pool.query(query, queryParams);

    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT u.id) as total
      FROM users u
      LEFT JOIN user_profiles up ON u.id = up.user_id
      ${whereClause}
    `;

    const countResult = await pool.query(countQuery, queryParams.slice(0, paramCount));

    res.json({
      success: true,
      data: {
        users: result.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: parseInt(countResult.rows[0].total),
          pages: Math.ceil(countResult.rows[0].total / limit)
        }
      }
    });

  } catch (error) {
    next(error);
  }
};

// Update user role or status
exports.updateUser = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { role, email_verified, status } = req.body;

    const updates = [];
    const values = [];
    let paramCount = 0;

    if (role) {
      paramCount++;
      updates.push(`role = $${paramCount}`);
      values.push(role);
    }

    if (typeof email_verified === 'boolean') {
      paramCount++;
      updates.push(`email_verified = $${paramCount}`);
      values.push(email_verified);
    }

    if (updates.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'NO_UPDATES',
          message: 'No valid updates provided'
        }
      });
    }

    paramCount++;
    values.push(userId);

    const query = `
      UPDATE users 
      SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await pool.query(query, values);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        user: result.rows[0]
      }
    });

  } catch (error) {
    next(error);
  }
};

// Get all agents for admin management
exports.getAgentsAdmin = async (req, res, next) => {
  try {
    const { page = 1, limit = 20, status = '', category = '' } = req.query;
    const offset = (page - 1) * limit;

    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramCount = 0;

    if (status) {
      paramCount++;
      whereClause += ` AND a.status = $${paramCount}`;
      queryParams.push(status);
    }

    if (category) {
      paramCount++;
      whereClause += ` AND a.category = $${paramCount}`;
      queryParams.push(category);
    }

    const query = `
      SELECT 
        a.*,
        up.username as creator_username,
        COUNT(DISTINCT au.user_id) as unique_users,
        COUNT(au.id) as total_usage,
        SUM(au.cost) as total_revenue
      FROM agents a
      LEFT JOIN user_profiles up ON a.creator_id = up.user_id
      LEFT JOIN agent_usage au ON a.id = au.agent_id
      ${whereClause}
      GROUP BY a.id, up.username
      ORDER BY a.created_at DESC
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    queryParams.push(limit, offset);
    const result = await pool.query(query, queryParams);

    res.json({
      success: true,
      data: {
        agents: result.rows
      }
    });

  } catch (error) {
    next(error);
  }
};

// Update agent status
exports.updateAgentStatus = async (req, res, next) => {
  try {
    const { agentId } = req.params;
    const { status } = req.body;

    if (!['active', 'inactive', 'pending', 'deleted'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_STATUS',
          message: 'Invalid status value'
        }
      });
    }

    const result = await pool.query(
      'UPDATE agents SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *',
      [status, agentId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        agent: result.rows[0]
      }
    });

  } catch (error) {
    next(error);
  }
};

// Get system usage statistics
exports.getUsageStats = async (req, res, next) => {
  try {
    const { timeframe = '30 days' } = req.query;
    
    const stats = await getSystemUsageStats(timeframe);
    
    res.json({
      success: true,
      data: {
        usage_stats: stats,
        timeframe: timeframe
      }
    });

  } catch (error) {
    next(error);
  }
};
