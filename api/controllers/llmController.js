const axios = require('axios');
const https = require('https');
const http = require('http');
const { GoogleAuth } = require('google-auth-library');

const {
  GEMINI_API_KEY,
  OPENAI_API_KEY,
  ANTHROPIC_API_KEY,
  DEEPSEEK_API_KEY,
  GROK_API_KEY,
  XAI_API_KEY,
  GOOGLE_PROJECT_ID,
  VITE_ANTHROPIC_KEY_1,
  VITE_ANTHROPIC_KEY_2,
  VITE_ANTHROPIC_KEY_3,
} = process.env;

const httpsAgent = new https.Agent({ keepAlive: true });
const httpAgent = new http.Agent({ keepAlive: true });

const axiosInstance = axios.create({
  httpsAgent,
  httpAgent,
  timeout: 120000 // 2 minutes timeout
});

const auth = new GoogleAuth({
  scopes: [
    'https://www.googleapis.com/auth/cloud-platform',
    'https://www.googleapis.com/auth/generative-language',
  ],
});

// LLM API endpoints and configurations
const LLM_CONFIGS = {
  'gemini-1-5-pro': {
    apiKey: GEMINI_API_KEY,
    baseURL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-latest:generateContent',
    provider: 'google'
  },
  'gemini-2-0-flash': {
    apiKey: GEMINI_API_KEY,
    baseURL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent',
    provider: 'google'
  },
  'gpt-4o': {
    apiKey: OPENAI_API_KEY,
    baseURL: 'https://api.openai.com/v1/chat/completions',
    provider: 'openai',
    model: 'gpt-4o'
  },
  'claude-3-7-sonnet': {
    apiKey: ANTHROPIC_API_KEY,
    baseURL: 'https://api.anthropic.com/v1/messages',
    provider: 'anthropic',
    model: 'claude-3-5-sonnet-20241022'
  },
  'deepseek-v3-fw': {
    apiKey: DEEPSEEK_API_KEY,
    baseURL: 'https://api.deepseek.com/chat/completions',
    provider: 'deepseek',
    model: 'deepseek-chat'
  },
  'grok-2': {
    apiKey: XAI_API_KEY || GROK_API_KEY,
    baseURL: 'https://api.x.ai/v1/chat/completions',
    provider: 'xai',
    model: 'grok-2-1212'
  }
};

// Reusable function to handle Gemini requests
const handleGeminiRequest = async (prompt, systemPrompt) => {
  const parts = [];
  if (systemPrompt) parts.push({ text: `[SYSTEM]: ${systemPrompt}` });
  parts.push({ text: prompt });

  const client = await auth.getClient();
  const url = 'https://generativelanguage.googleapis.com/v1/models/gemini-1.5-pro-001:generateContent';
  
  const oauthRes = await client.request({
    url,
    method: 'POST',
    params: { key: GEMINI_API_KEY },
    headers: { 'X-Goog-User-Project': GOOGLE_PROJECT_ID },
    data: { contents: [{ parts }] },
    agent: httpsAgent,
  });

  return oauthRes.data.candidates?.[0]?.content?.parts?.[0]?.text;
};

// Generic LLM query handler
exports.queryLLM = async (req, res, next) => {
  try {
    const { prompt, model = 'gemini-1-5-pro', temperature = 0.7, max_tokens } = req.body;

    if (!prompt || !prompt.trim()) {
      return res.status(400).json({
        error: {
          code: 'INVALID_INPUT',
          message: 'Prompt is required'
        }
      });
    }

    const config = LLM_CONFIGS[model];
    if (!config) {
      return res.status(400).json({
        error: {
          code: 'INVALID_MODEL',
          message: `Model ${model} is not supported`
        }
      });
    }

    if (!config.apiKey) {
      return res.status(503).json({
        error: {
          code: 'SERVICE_UNAVAILABLE',
          message: `${config.provider} API key not configured`
        }
      });
    }

    let response;
    const startTime = Date.now();

    try {
      switch (config.provider) {
        case 'google':
          response = await queryGemini(prompt, config, temperature);
          break;
        case 'openai':
          response = await queryOpenAI(prompt, config, temperature, max_tokens);
          break;
        case 'anthropic':
          response = await queryClaude(prompt, config, temperature, max_tokens);
          break;
        case 'deepseek':
          response = await queryDeepSeek(prompt, config, temperature, max_tokens);
          break;
        case 'xai':
          response = await queryXAI(prompt, config, temperature, max_tokens);
          break;
        default:
          throw new Error(`Provider ${config.provider} not implemented`);
      }

      const duration = Date.now() - startTime;

      res.json({
        success: true,
        data: {
          response,
          model,
          provider: config.provider,
          duration_ms: duration
        }
      });

    } catch (apiError) {
      console.error(`[LLM] ${config.provider} API error:`, apiError.message);
      
      return res.status(502).json({
        error: {
          code: 'LLM_API_ERROR',
          message: `Failed to query ${config.provider}: ${apiError.message}`,
          provider: config.provider
        }
      });
    }

  } catch (error) {
    next(error);
  }
};

// Gemini implementation
async function queryGemini(prompt, config, temperature) {
  const payload = {
    contents: [{
      parts: [{ text: prompt }]
    }],
    generationConfig: {
      temperature,
      maxOutputTokens: 4096
    }
  };

  const response = await axiosInstance.post(
    `${config.baseURL}?key=${config.apiKey}`,
    payload,
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );

  const candidates = response.data.candidates;
  if (!candidates || candidates.length === 0) {
    throw new Error('No response from Gemini');
  }

  return candidates[0].content.parts[0].text;
}

// OpenAI implementation
async function queryOpenAI(prompt, config, temperature, max_tokens = 4096) {
  const payload = {
    model: config.model,
    messages: [
      { role: 'user', content: prompt }
    ],
    temperature,
    max_tokens
  };

  const response = await axiosInstance.post(
    config.baseURL,
    payload,
    {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.data.choices[0].message.content;
}

// Anthropic Claude implementation
async function queryClaude(prompt, config, temperature, max_tokens = 4096) {
  const payload = {
    model: config.model,
    max_tokens,
    temperature,
    messages: [
      { role: 'user', content: prompt }
    ]
  };

  const response = await axiosInstance.post(
    config.baseURL,
    payload,
    {
      headers: {
        'x-api-key': config.apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      }
    }
  );

  return response.data.content[0].text;
}

// DeepSeek implementation
async function queryDeepSeek(prompt, config, temperature, max_tokens = 4096) {
  const payload = {
    model: config.model,
    messages: [
      { role: 'user', content: prompt }
    ],
    temperature,
    max_tokens,
    stream: false
  };

  const response = await axiosInstance.post(
    config.baseURL,
    payload,
    {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.data.choices[0].message.content;
}

// X.AI (Grok) implementation
async function queryXAI(prompt, config, temperature, max_tokens = 4096) {
  const payload = {
    model: config.model,
    messages: [
      { role: 'user', content: prompt }
    ],
    temperature,
    max_tokens,
    stream: false
  };

  const response = await axiosInstance.post(
    config.baseURL,
    payload,
    {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      }
    }
  );

  return response.data.choices[0].message.content;
}

// Specialized endpoints for specific models
exports.queryGemini = async (req, res, next) => {
  req.body.model = 'gemini-1-5-pro';
  return exports.queryLLM(req, res, next);
};

exports.queryGPT4 = async (req, res, next) => {
  req.body.model = 'gpt-4o';
  return exports.queryLLM(req, res, next);
};

exports.queryClaude = async (req, res, next) => {
  req.body.model = 'claude-3-7-sonnet';
  return exports.queryLLM(req, res, next);
};

exports.queryDeepSeek = async (req, res, next) => {
  req.body.model = 'deepseek-v3-fw';
  return exports.queryLLM(req, res, next);
};

exports.queryGrok = async (req, res, next) => {
  req.body.model = 'grok-2';
  return exports.queryLLM(req, res, next);
};

// Get available models
exports.getModels = async (req, res, next) => {
  try {
    const models = Object.keys(LLM_CONFIGS).map(key => {
      const config = LLM_CONFIGS[key];
      return {
        id: key,
        name: key,
        provider: config.provider,
        available: !!config.apiKey
      };
    });

    res.json({
      success: true,
      data: { models }
    });
  } catch (error) {
    next(error);
  }
};

// Health check for LLM services
exports.healthCheck = async (req, res, next) => {
  try {
    const health = {};
    
    for (const [modelId, config] of Object.entries(LLM_CONFIGS)) {
      health[modelId] = {
        provider: config.provider,
        configured: !!config.apiKey,
        status: config.apiKey ? 'available' : 'not_configured'
      };
    }

    res.json({
      success: true,
      data: { health }
    });
  } catch (error) {
    next(error);
  }
}; 