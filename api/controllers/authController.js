const crypto = require('crypto');
const nacl = require('tweetnacl');
const bs58 = require('bs58');
const { pool } = require('../../lib/database');
const { generateToken, hashPassword, comparePassword, generateRandomToken } = require('../../lib/auth');
const { OAuth2Client } = require('google-auth-library');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

const googleClient = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const SALT_ROUNDS = 12;

// Helper function to create user profile
const createUserProfile = async (userId, username, email) => {
  try {
    await pool.query(
      `INSERT INTO user_profiles (user_id, username, email) 
       VALUES ($1, $2, $3)
       ON CONFLICT (user_id) DO NOTHING`,
      [userId, username, email]
    );
  } catch (error) {
    console.error('[Auth] Error creating user profile:', error);
  }
};

// Generate a nonce for a user to sign
exports.getNonce = async (req, res, next) => {
  try {
    const nonce = generateRandomToken();
    
    // Store nonce temporarily (in production, use Redis or similar)
    req.session = req.session || {};
    req.session.nonce = nonce;

    res.json({ nonce });
  } catch (error) {
    next(error);
  }
};

// Google OAuth sign-in
exports.signInWithGoogle = async (req, res, next) => {
  try {
    const { idToken } = req.body;

    if (!idToken) {
      return res.status(400).json({
        error: {
          code: 'MISSING_TOKEN',
          message: 'Google ID token is required'
        }
      });
    }

    // Verify the Google ID token
    let ticket;
    try {
      ticket = await googleClient.verifyIdToken({
        idToken,
        audience: process.env.GOOGLE_CLIENT_ID,
      });
    } catch (error) {
      return res.status(400).json({
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid Google ID token'
        }
      });
    }

    const payload = ticket.getPayload();
    const googleId = payload.sub;
    const email = payload.email;
    const username = payload.email.split('@')[0];
    const avatarUrl = payload.picture || null;

    // Check if user exists with this Google ID
    let userResult = await pool.query(
      'SELECT * FROM users WHERE google_id = $1',
      [googleId]
    );

    let user;
    if (userResult.rows.length === 0) {
      // Check if user exists with this email
      const emailResult = await pool.query(
        'SELECT * FROM users WHERE email = $1',
        [email]
      );

      if (emailResult.rows.length > 0) {
        // Link Google account to existing user
        await pool.query(
          'UPDATE users SET google_id = $1, avatar_url = COALESCE(avatar_url, $2) WHERE email = $3',
          [googleId, avatarUrl, email]
        );
        user = emailResult.rows[0];
      } else {
        // Create new user
        const insertResult = await pool.query(
          `INSERT INTO users (email, username, google_id, avatar_url, email_verified, created_at)
           VALUES ($1, $2, $3, $4, true, NOW())
           RETURNING *`,
          [email, username, googleId, avatarUrl]
        );
        user = insertResult.rows[0];
      }
    } else {
      user = userResult.rows[0];
    }

    const token = generateToken(user.id);

    res.json({
      data: {
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          avatar_url: user.avatar_url,
          bio: user.bio,
          website: user.website,
          wallet_address: user.wallet_address,
          email_verified: user.email_verified,
          created_at: user.created_at
        },
        token
      }
    });
  } catch (error) {
    next(error);
  }
};

// Sign up with email and password
exports.signUp = async (req, res, next) => {
  try {
    const { email, password, username } = req.body;

    // Check if user already exists
    const existingUser = await pool.query(
      'SELECT id FROM users WHERE email = $1',
      [email.toLowerCase()]
    );

    if (existingUser.rows.length > 0) {
      return res.status(400).json({
        error: {
          code: 'USER_EXISTS',
          message: 'A user with this email already exists'
        }
      });
    }

    // Check if username is taken
    const existingUsername = await pool.query(
      'SELECT id FROM user_profiles WHERE username = $1',
      [username.toLowerCase()]
    );

    if (existingUsername.rows.length > 0) {
      return res.status(400).json({
        error: {
          code: 'USERNAME_TAKEN',
          message: 'This username is already taken'
        }
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);

    // Create user
    const userResult = await pool.query(
      `INSERT INTO users (email, password_hash, email_verified, created_at, updated_at)
       VALUES ($1, $2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
       RETURNING id, email, email_verified, created_at`,
      [email.toLowerCase(), hashedPassword]
    );

    const user = userResult.rows[0];

    // Create user profile
    await createUserProfile(user.id, username, email);

    // Generate JWT token
    const token = generateToken(user.id);

    res.status(201).json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        username: username,
        email_verified: user.email_verified,
        created_at: user.created_at
      },
      token
    });

  } catch (error) {
    next(error);
  }
};

// Sign in with email and password
exports.signIn = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Get user by email
    const userResult = await pool.query(
      `SELECT u.*, up.username, up.avatar_url, up.bio, up.website
       FROM users u
       LEFT JOIN user_profiles up ON u.id = up.user_id
       WHERE u.email = $1`,
      [email.toLowerCase()]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({
        error: {
          code: 'INVALID_CREDENTIALS',
          message: 'Invalid email or password'
        }
      });
    }

    const user = userResult.rows[0];

    // Verify password
    const validPassword = await bcrypt.compare(password, user.password_hash);
    if (!validPassword) {
      return res.status(401).json({
        error: {
          code: 'INVALID_CREDENTIALS',
          message: 'Invalid email or password'
        }
      });
    }

    // Generate JWT token
    const token = generateToken(user.id);

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        avatar_url: user.avatar_url,
        bio: user.bio,
        website: user.website,
        email_verified: user.email_verified,
        created_at: user.created_at
      },
      token
    });

  } catch (error) {
    next(error);
  }
};

// Google OAuth sign in
exports.googleAuth = async (req, res, next) => {
  try {
    const { idToken } = req.body;

    // Verify Google ID token
    const ticket = await googleClient.verifyIdToken({
      idToken,
      audience: process.env.GOOGLE_CLIENT_ID
    });

    const payload = ticket.getPayload();
    const { email, name, picture, email_verified } = payload;

    // Check if user exists
    let userResult = await pool.query(
      `SELECT u.*, up.username, up.avatar_url, up.bio, up.website
       FROM users u
       LEFT JOIN user_profiles up ON u.id = up.user_id
       WHERE u.email = $1`,
      [email.toLowerCase()]
    );

    let user;

    if (userResult.rows.length === 0) {
      // Create new user
      const newUserResult = await pool.query(
        `INSERT INTO users (email, email_verified, created_at, updated_at)
         VALUES ($1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
         RETURNING id, email, email_verified, created_at`,
        [email.toLowerCase(), email_verified]
      );

      user = newUserResult.rows[0];

      // Create user profile with Google info
      const username = email.split('@')[0].toLowerCase().replace(/[^a-z0-9_-]/g, '');
      await pool.query(
        `INSERT INTO user_profiles (user_id, username, email, avatar_url)
         VALUES ($1, $2, $3, $4)`,
        [user.id, username, email, picture]
      );

      user.username = username;
      user.avatar_url = picture;
    } else {
      user = userResult.rows[0];
      
      // Update email verification if Google account is verified
      if (email_verified && !user.email_verified) {
        await pool.query(
          'UPDATE users SET email_verified = true WHERE id = $1',
          [user.id]
        );
        user.email_verified = true;
      }
    }

    // Generate JWT token
    const token = generateToken(user.id);

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        avatar_url: user.avatar_url,
        bio: user.bio,
        website: user.website,
        email_verified: user.email_verified,
        created_at: user.created_at
      },
      token
    });

  } catch (error) {
    console.error('[Auth] Google auth error:', error);
    next(error);
  }
};

// Generate nonce for wallet authentication
exports.generateNonce = async (req, res, next) => {
  try {
    const nonce = crypto.randomBytes(32).toString('hex');
    
    // Store nonce in session (expires in 15 minutes)
    req.session.walletNonce = nonce;
    req.session.nonceExpires = Date.now() + (15 * 60 * 1000);

    res.json({ success: true, nonce });

  } catch (error) {
    next(error);
  }
};

// Verify wallet signature and authenticate
exports.verifyWallet = async (req, res, next) => {
  try {
    const { publicKey, signature, nonce } = req.body;

    // Verify nonce
    if (!req.session.walletNonce || req.session.walletNonce !== nonce) {
      return res.status(400).json({
        error: {
          code: 'INVALID_NONCE',
          message: 'Invalid or expired nonce'
        }
      });
    }

    if (Date.now() > req.session.nonceExpires) {
      return res.status(400).json({
        error: {
          code: 'NONCE_EXPIRED',
          message: 'Nonce has expired'
        }
      });
    }

    // Verify signature
    const message = `Please sign this message to log in to Agora Marketplace. Nonce: ${nonce}`;
    const messageBytes = new TextEncoder().encode(message);
    const publicKeyBytes = bs58.decode(publicKey);
    const signatureBytes = new Uint8Array(signature);

    const isValid = nacl.sign.detached.verify(messageBytes, signatureBytes, publicKeyBytes);

    if (!isValid) {
      return res.status(401).json({
        error: {
          code: 'INVALID_SIGNATURE',
          message: 'Invalid wallet signature'
        }
      });
    }

    // Clear nonce from session
    delete req.session.walletNonce;
    delete req.session.nonceExpires;

    // Check if user exists with this wallet
    let userResult = await pool.query(
      `SELECT u.*, up.username, up.avatar_url, up.bio, up.website
       FROM users u
       LEFT JOIN user_profiles up ON u.id = up.user_id
       WHERE up.wallet_address = $1`,
      [publicKey]
    );

    let user;

    if (userResult.rows.length === 0) {
      // Create new user with wallet
      const newUserResult = await pool.query(
        `INSERT INTO users (email_verified, created_at, updated_at)
         VALUES (false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
         RETURNING id, email_verified, created_at`,
        []
      );

      user = newUserResult.rows[0];

      // Create user profile with wallet
      const username = `user_${publicKey.slice(-8)}`;
      await pool.query(
        `INSERT INTO user_profiles (user_id, username, wallet_address, wallet_connected_at)
         VALUES ($1, $2, $3, CURRENT_TIMESTAMP)`,
        [user.id, username, publicKey]
      );

      user.username = username;
      user.wallet_address = publicKey;
    } else {
      user = userResult.rows[0];
    }

    // Generate JWT token
    const token = generateToken(user.id);

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        avatar_url: user.avatar_url,
        bio: user.bio,
        website: user.website,
        wallet_address: user.wallet_address,
        email_verified: user.email_verified,
        created_at: user.created_at
      },
      token
    });

  } catch (error) {
    next(error);
  }
};

// Get current user
exports.getCurrentUser = async (req, res, next) => {
  try {
    const userId = req.user.id;

    const userResult = await pool.query(
      `SELECT u.*, up.username, up.avatar_url, up.bio, up.website, up.wallet_address
       FROM users u
       LEFT JOIN user_profiles up ON u.id = up.user_id
       WHERE u.id = $1`,
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        }
      });
    }

    const user = userResult.rows[0];

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        avatar_url: user.avatar_url,
        bio: user.bio,
        website: user.website,
        wallet_address: user.wallet_address,
        email_verified: user.email_verified,
        created_at: user.created_at
      }
    });

  } catch (error) {
    next(error);
  }
}; 