const { pool } = require('../../lib/database');

// Get chat messages for a user and optional agent
exports.getMessages = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { agentId } = req.query;
    
    let query = `
      SELECT cm.*, a.name as agent_name
      FROM chat_messages cm
      LEFT JOIN agents a ON cm.agent_id = a.id
      WHERE cm.user_id = $1
    `;
    
    const params = [userId];
    
    if (agentId) {
      query += ` AND cm.agent_id = $2`;
      params.push(agentId);
    }
    
    query += ` ORDER BY cm.created_at ASC`;
    
    const result = await pool.query(query, params);
    res.json({ success: true, messages: result.rows });

  } catch (error) {
    next(error);
  }
};

// Send a new message
exports.sendMessage = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { content, is_markdown = false, agent_id } = req.body;

    if (!content || !content.trim()) {
      return res.status(400).json({
        error: {
          code: 'INVALID_INPUT',
          message: 'Message content is required'
        }
      });
    }

    const result = await pool.query(
      `INSERT INTO chat_messages (user_id, agent_id, content, is_bot, is_markdown)
       VALUES ($1, $2, $3, false, $4)
       RETURNING *`,
      [userId, agent_id, content.trim(), is_markdown]
    );

    res.status(201).json({ success: true, message: result.rows[0] });

  } catch (error) {
    next(error);
  }
};

// Save bot response
exports.saveBotResponse = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { content, is_markdown = true, agent_id } = req.body;

    if (!content || !content.trim()) {
      return res.status(400).json({
        error: {
          code: 'INVALID_INPUT',
          message: 'Bot response content is required'
        }
      });
    }

    const result = await pool.query(
      `INSERT INTO chat_messages (user_id, agent_id, content, is_bot, is_markdown)
       VALUES ($1, $2, $3, true, $4)
       RETURNING *`,
      [userId, agent_id, content.trim(), is_markdown]
    );

    res.status(201).json({ success: true, message: result.rows[0] });

  } catch (error) {
    next(error);
  }
};

// Clear messages for user (optionally for specific agent)
exports.clearMessages = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { agentId } = req.query;

    let query = 'DELETE FROM chat_messages WHERE user_id = $1';
    const params = [userId];

    if (agentId) {
      query += ' AND agent_id = $2';
      params.push(agentId);
    }

    await pool.query(query, params);
    res.json({ success: true, message: 'Messages cleared' });

  } catch (error) {
    next(error);
  }
};

// Update user agent interaction history
exports.updateInteractionHistory = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { agent_id, duration_ms } = req.body;

    await pool.query(
      `INSERT INTO user_agent_history (user_id, agent_id, total_interactions, total_duration_ms, last_interaction_at)
       VALUES ($1, $2, 1, $3, CURRENT_TIMESTAMP)
       ON CONFLICT (user_id, agent_id) 
       DO UPDATE SET
         total_interactions = user_agent_history.total_interactions + 1,
         total_duration_ms = user_agent_history.total_duration_ms + $3,
         last_interaction_at = CURRENT_TIMESTAMP`,
      [userId, agent_id, duration_ms || 0]
    );

    res.json({ success: true, message: 'Interaction history updated' });

  } catch (error) {
    next(error);
  }
};

// Get chat statistics
exports.getChatStats = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Get overall stats
    const statsQuery = `
      SELECT 
        COUNT(*) as total_messages,
        COUNT(DISTINCT agent_id) as unique_agents,
        COUNT(CASE WHEN is_bot = false THEN 1 END) as user_messages,
        COUNT(CASE WHEN is_bot = true THEN 1 END) as bot_messages,
        MIN(created_at) as first_message,
        MAX(created_at) as last_message
      FROM chat_messages
      WHERE user_id = $1
    `;

    // Get top agents by message count
    const topAgentsQuery = `
      SELECT 
        a.name,
        a.slug,
        a.image_url,
        COUNT(m.id) as message_count
      FROM chat_messages m
      JOIN agents a ON m.agent_id = a.id
      WHERE m.user_id = $1
      GROUP BY a.id, a.name, a.slug, a.image_url
      ORDER BY message_count DESC
      LIMIT 5
    `;

    const [statsResult, topAgentsResult] = await Promise.all([
      pool.query(statsQuery, [userId]),
      pool.query(topAgentsQuery, [userId])
    ]);

    const stats = statsResult.rows[0];
    const topAgents = topAgentsResult.rows;

    res.json({
      success: true,
      data: {
        stats: {
          totalMessages: parseInt(stats.total_messages),
          uniqueAgents: parseInt(stats.unique_agents),
          userMessages: parseInt(stats.user_messages),
          botMessages: parseInt(stats.bot_messages),
          firstMessage: stats.first_message,
          lastMessage: stats.last_message
        },
        topAgents
      }
    });
  } catch (error) {
    next(error);
  }
};

// Export chat history
exports.exportChatHistory = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { agent_id, format = 'json' } = req.query;

    let whereClause = 'WHERE m.user_id = $1';
    const queryParams = [userId];

    if (agent_id) {
      whereClause += ' AND m.agent_id = $2';
      queryParams.push(agent_id);
    }

    const query = `
      SELECT 
        m.*,
        a.name as agent_name,
        a.slug as agent_slug
      FROM chat_messages m
      LEFT JOIN agents a ON m.agent_id = a.id
      ${whereClause}
      ORDER BY m.created_at ASC
    `;

    const result = await pool.query(query, queryParams);

    if (format === 'txt') {
      // Plain text format
      const textContent = result.rows
        .map(msg => {
          const timestamp = new Date(msg.created_at).toISOString();
          const sender = msg.is_bot ? (msg.agent_name || 'Bot') : 'You';
          return `[${timestamp}] ${sender}: ${msg.content}`;
        })
        .join('\n');

      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Content-Disposition', 'attachment; filename="chat-history.txt"');
      res.send(textContent);
    } else {
      // JSON format
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename="chat-history.json"');
      res.json({
        exportedAt: new Date().toISOString(),
        totalMessages: result.rows.length,
        messages: result.rows
      });
    }

  } catch (error) {
    next(error);
  }
}; 