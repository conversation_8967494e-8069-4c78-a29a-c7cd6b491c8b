const { pool } = require('../../lib/database');
const { Connection, PublicKey, Transaction, SystemProgram, LAMPORTS_PER_SOL } = require('@solana/web3.js');
const { Token, TOKEN_PROGRAM_ID } = require('@solana/spl-token');

// Solana connection
const connection = new Connection(process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com');

// dGPU token mint address (replace with actual mint address)
const DGPU_MINT_ADDRESS = process.env.DGPU_TOKEN_MINT || '********************************'; // System program as placeholder
const DGPU_MINT = new PublicKey(DGPU_MINT_ADDRESS);

// Payment wallet (replace with actual payment wallet)
const PAYMENT_WALLET_ADDRESS = process.env.PAYMENT_WALLET || '********************************'; // System program as placeholder
const PAYMENT_WALLET = new PublicKey(PAYMENT_WALLET_ADDRESS);

// Get user's dGPU balance
exports.getBalance = async (req, res, next) => {
  try {
    const userId = req.user.id;
    
    // Get user's wallet address
    const userResult = await pool.query(
      'SELECT wallet_address FROM user_profiles WHERE user_id = $1',
      [userId]
    );

    if (userResult.rows.length === 0 || !userResult.rows[0].wallet_address) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'WALLET_NOT_CONNECTED',
          message: 'Please connect your wallet first'
        }
      });
    }

    const walletAddress = userResult.rows[0].wallet_address;
    const userPublicKey = new PublicKey(walletAddress);

    try {
      // Get dGPU token balance
      const tokenAccounts = await connection.getTokenAccountsByOwner(userPublicKey, {
        mint: DGPU_MINT
      });

      let balance = 0;
      if (tokenAccounts.value.length > 0) {
        const accountInfo = await connection.getTokenAccountBalance(tokenAccounts.value[0].pubkey);
        balance = parseFloat(accountInfo.value.uiAmount || 0);
      }

      // Also get SOL balance for transaction fees
      const solBalance = await connection.getBalance(userPublicKey);
      const solBalanceInSol = solBalance / LAMPORTS_PER_SOL;

      res.json({
        success: true,
        data: {
          dgpu_balance: balance,
          sol_balance: solBalanceInSol,
          wallet_address: walletAddress
        }
      });

    } catch (solanaError) {
      console.error('Solana error:', solanaError);
      res.json({
        success: true,
        data: {
          dgpu_balance: 0,
          sol_balance: 0,
          wallet_address: walletAddress,
          error: 'Unable to fetch on-chain balance'
        }
      });
    }

  } catch (error) {
    next(error);
  }
};

// Create payment transaction for agent usage
exports.createPayment = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { agent_id, amount, usage_type = 'chat' } = req.body;

    // Validate amount
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_AMOUNT',
          message: 'Invalid payment amount'
        }
      });
    }

    // Get agent details
    const agentResult = await pool.query(
      'SELECT * FROM agents WHERE id = $1 AND status = $2',
      [agent_id, 'active']
    );

    if (agentResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent not found'
        }
      });
    }

    const agent = agentResult.rows[0];

    // Get user's wallet address
    const userResult = await pool.query(
      'SELECT wallet_address FROM user_profiles WHERE user_id = $1',
      [userId]
    );

    if (userResult.rows.length === 0 || !userResult.rows[0].wallet_address) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'WALLET_NOT_CONNECTED',
          message: 'Please connect your wallet first'
        }
      });
    }

    const walletAddress = userResult.rows[0].wallet_address;

    // Create payment record
    const paymentResult = await pool.query(
      `INSERT INTO payments (
        user_id, agent_id, amount, currency, status, usage_type, 
        user_wallet, payment_wallet, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP)
      RETURNING *`,
      [userId, agent_id, amount, 'dGPU', 'pending', usage_type, walletAddress, PAYMENT_WALLET.toString()]
    );

    const payment = paymentResult.rows[0];

    res.json({
      success: true,
      data: {
        payment_id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        agent: {
          id: agent.id,
          name: agent.name,
          price: agent.price
        },
        payment_wallet: PAYMENT_WALLET.toString(),
        user_wallet: walletAddress
      }
    });

  } catch (error) {
    next(error);
  }
};

// Confirm payment transaction
exports.confirmPayment = async (req, res, next) => {
  try {
    const { payment_id, transaction_signature } = req.body;
    const userId = req.user.id;

    // Get payment record
    const paymentResult = await pool.query(
      'SELECT * FROM payments WHERE id = $1 AND user_id = $2',
      [payment_id, userId]
    );

    if (paymentResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'PAYMENT_NOT_FOUND',
          message: 'Payment not found'
        }
      });
    }

    const payment = paymentResult.rows[0];

    if (payment.status !== 'pending') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'PAYMENT_ALREADY_PROCESSED',
          message: 'Payment already processed'
        }
      });
    }

    try {
      // Verify transaction on Solana
      const transaction = await connection.getTransaction(transaction_signature, {
        commitment: 'confirmed'
      });

      if (!transaction) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'TRANSACTION_NOT_FOUND',
            message: 'Transaction not found on blockchain'
          }
        });
      }

      // Update payment status
      await pool.query(
        `UPDATE payments 
         SET status = $1, transaction_signature = $2, confirmed_at = CURRENT_TIMESTAMP
         WHERE id = $3`,
        ['confirmed', transaction_signature, payment_id]
      );

      // Credit user's account or update usage allowance
      await pool.query(
        `INSERT INTO user_credits (user_id, agent_id, credits, source, created_at)
         VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)`,
        [userId, payment.agent_id, payment.amount, 'payment']
      );

      res.json({
        success: true,
        data: {
          payment_id: payment.id,
          status: 'confirmed',
          transaction_signature: transaction_signature
        }
      });

    } catch (solanaError) {
      console.error('Solana verification error:', solanaError);
      
      // Mark payment as failed
      await pool.query(
        `UPDATE payments 
         SET status = $1, error_message = $2
         WHERE id = $3`,
        ['failed', solanaError.message, payment_id]
      );

      return res.status(400).json({
        success: false,
        error: {
          code: 'TRANSACTION_VERIFICATION_FAILED',
          message: 'Failed to verify transaction on blockchain'
        }
      });
    }

  } catch (error) {
    next(error);
  }
};

// Get payment history
exports.getPaymentHistory = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const query = `
      SELECT 
        p.*,
        a.name as agent_name,
        a.slug as agent_slug
      FROM payments p
      LEFT JOIN agents a ON p.agent_id = a.id
      WHERE p.user_id = $1
      ORDER BY p.created_at DESC
      LIMIT $2 OFFSET $3
    `;

    const result = await pool.query(query, [userId, limit, offset]);

    res.json({
      success: true,
      data: {
        payments: result.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: result.rows.length
        }
      }
    });

  } catch (error) {
    next(error);
  }
};

// Get user credits
exports.getUserCredits = async (req, res, next) => {
  try {
    const userId = req.user.id;

    const query = `
      SELECT 
        agent_id,
        SUM(credits) as total_credits,
        a.name as agent_name,
        a.slug as agent_slug
      FROM user_credits uc
      LEFT JOIN agents a ON uc.agent_id = a.id
      WHERE uc.user_id = $1
      GROUP BY agent_id, a.name, a.slug
      HAVING SUM(credits) > 0
      ORDER BY total_credits DESC
    `;

    const result = await pool.query(query, [userId]);

    res.json({
      success: true,
      data: {
        credits: result.rows
      }
    });

  } catch (error) {
    next(error);
  }
};
