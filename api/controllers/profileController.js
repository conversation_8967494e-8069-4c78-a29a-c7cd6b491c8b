const { pool } = require('../../lib/database');

// Get user profile by username
exports.getProfile = async (req, res, next) => {
  try {
    const { username } = req.params;
    
    const result = await pool.query(
      `SELECT up.*, u.email, u.created_at as user_created_at
       FROM user_profiles up
       JOIN users u ON up.user_id = u.id
       WHERE up.username = $1`,
      [username]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'PROFILE_NOT_FOUND',
          message: 'Profile not found'
        }
      });
    }

    const profile = result.rows[0];
    res.json({ success: true, profile });

  } catch (error) {
    next(error);
  }
};

// Get user settings
exports.getSettings = async (req, res, next) => {
  try {
    const userId = req.user.id;
    
    const result = await pool.query(
      'SELECT * FROM user_settings WHERE user_id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      // Create default settings if not exists
      const defaultResult = await pool.query(
        'INSERT INTO user_settings (user_id) VALUES ($1) RETURNING *',
        [userId]
      );
      return res.json({ success: true, settings: defaultResult.rows[0] });
    }

    res.json({ success: true, settings: result.rows[0] });

  } catch (error) {
    next(error);
  }
};

// Update user settings
exports.updateSettings = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const {
      theme,
      show_email,
      bio,
      avatar_url,
      website,
      social_links,
      notification_preferences
    } = req.body;

    const result = await pool.query(
      `UPDATE user_settings 
       SET theme = COALESCE($1, theme),
           show_email = COALESCE($2, show_email),
           bio = COALESCE($3, bio),
           avatar_url = COALESCE($4, avatar_url),
           website = COALESCE($5, website),
           social_links = COALESCE($6, social_links),
           notification_preferences = COALESCE($7, notification_preferences),
           updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $8
       RETURNING *`,
      [theme, show_email, bio, avatar_url, website, social_links, notification_preferences, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: {
          code: 'SETTINGS_NOT_FOUND',
          message: 'User settings not found'
        }
      });
    }

    res.json({ success: true, settings: result.rows[0] });

  } catch (error) {
    next(error);
  }
};

// Get user bookmarks
exports.getBookmarks = async (req, res, next) => {
  try {
    const userId = req.user.id;
    
    const result = await pool.query(
      `SELECT ab.*, a.name, a.description, a.image_url, a.category, a.slug
       FROM agent_bookmarks ab
       JOIN agents a ON ab.agent_id = a.id
       WHERE ab.user_id = $1
       ORDER BY ab.created_at DESC`,
      [userId]
    );

    const bookmarks = result.rows.map(row => ({
      id: row.id,
      agent_id: row.agent_id,
      notes: row.notes,
      created_at: row.created_at,
      agent: {
        name: row.name,
        description: row.description,
        image_url: row.image_url,
        category: row.category,
        slug: row.slug
      }
    }));

    res.json({ success: true, bookmarks });

  } catch (error) {
    next(error);
  }
};

// Add bookmark
exports.addBookmark = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { agent_id, notes } = req.body;

    // Check if bookmark already exists
    const existingResult = await pool.query(
      'SELECT id FROM agent_bookmarks WHERE user_id = $1 AND agent_id = $2',
      [userId, agent_id]
    );

    if (existingResult.rows.length > 0) {
      return res.status(400).json({
        error: {
          code: 'BOOKMARK_EXISTS',
          message: 'Agent is already bookmarked'
        }
      });
    }

    // Check if agent exists
    const agentResult = await pool.query(
      'SELECT id, name FROM agents WHERE id = $1 AND status = $2',
      [agent_id, 'active']
    );

    if (agentResult.rows.length === 0) {
      return res.status(404).json({
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent not found or inactive'
        }
      });
    }

    const result = await pool.query(
      `INSERT INTO agent_bookmarks (user_id, agent_id, notes)
       VALUES ($1, $2, $3)
       RETURNING *`,
      [userId, agent_id, notes]
    );

    res.status(201).json({ success: true, bookmark: result.rows[0] });

  } catch (error) {
    next(error);
  }
};

// Remove bookmark
exports.removeBookmark = async (req, res, next) => {
  try {
    const userId = req.user.id;   
    const { bookmarkId } = req.params;

    const result = await pool.query(
      'DELETE FROM agent_bookmarks WHERE id = $1 AND user_id = $2 RETURNING *',
      [bookmarkId, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: {
          code: 'BOOKMARK_NOT_FOUND',
          message: 'Bookmark not found'
        }
      });
    }

    res.json({ success: true, message: 'Bookmark removed' });

  } catch (error) {
    next(error);
  }
};

// Get user history
exports.getHistory = async (req, res, next) => {
  try {
    const userId = req.user.id;
    
    const result = await pool.query(
      `SELECT uah.*, a.name, a.description, a.image_url, a.category, a.slug
       FROM user_agent_history uah
       JOIN agents a ON uah.agent_id = a.id
       WHERE uah.user_id = $1
       ORDER BY uah.last_interaction_at DESC`,
      [userId]
    );

    const history = result.rows.map(row => ({
      id: row.id,
      agent_id: row.agent_id,
      total_interactions: row.total_interactions,
      total_tokens: row.total_tokens,
      total_duration_ms: row.total_duration_ms,
      first_interaction_at: row.first_interaction_at,
      last_interaction_at: row.last_interaction_at,
      agent: {
        name: row.name,
        description: row.description,
        image_url: row.image_url,
        category: row.category,
        slug: row.slug
      }
    }));

    res.json({ success: true, history });

  } catch (error) {
    next(error);
  }
};

// Clear history
exports.clearHistory = async (req, res, next) => {
  try {
    const userId = req.user.id;

    await pool.query(
      'DELETE FROM user_agent_history WHERE user_id = $1',
      [userId]
    );

    res.json({ success: true, message: 'History cleared' });

  } catch (error) {
    next(error);
  }
};

// Get dashboard stats
exports.getDashboardStats = async (req, res, next) => {
  try {
    const userId = req.user.id;

    const result = await pool.query(
      'SELECT get_user_dashboard_stats($1) as stats',
      [userId]
    );

    const stats = result.rows[0].stats;
    res.json({ success: true, stats });

  } catch (error) {
    next(error);
  }
};

// Get created agents
exports.getCreatedAgents = async (req, res, next) => {
  try {
    const { userId } = req.params;

    const result = await pool.query(
      'SELECT id, name, slug, category, image_url, status, created_at FROM agents WHERE creator = $1 ORDER BY created_at DESC',
      [userId]
    );

    res.json({ success: true, agents: result.rows });

  } catch (error) {
    next(error);
  }
};

// Update user profile
exports.updateProfile = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { username, bio, website, avatar_url } = req.body;

    // Check if username is taken by another user
    if (username) {
      const existingResult = await pool.query(
        'SELECT id FROM user_profiles WHERE username = $1 AND user_id != $2',
        [username, userId]
      );

      if (existingResult.rows.length > 0) {
        return res.status(400).json({
          error: {
            code: 'USERNAME_TAKEN',
            message: 'Username is already taken'
          }
        });
      }
    }

    const result = await pool.query(
      `UPDATE user_profiles 
       SET username = COALESCE($1, username),
           bio = COALESCE($2, bio),
           website = COALESCE($3, website),
           avatar_url = COALESCE($4, avatar_url),
           updated_at = CURRENT_TIMESTAMP
       WHERE user_id = $5
       RETURNING *`,
      [username, bio, website, avatar_url, userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: {
          code: 'PROFILE_NOT_FOUND',
          message: 'Profile not found'
        }
      });
    }

    res.json({ success: true, profile: result.rows[0] });

  } catch (error) {
    next(error);
  }
}; 