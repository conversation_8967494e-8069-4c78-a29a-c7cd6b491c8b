const { pool } = require('../../lib/database');
const slugify = require('slugify');

// Get all agents with filtering and pagination
exports.getAgents = async (req, res, next) => {
  try {
    const { 
      category, 
      search, 
      sortBy = 'created_at', 
      order = 'DESC', 
      page = 1, 
      limit = 20,
      featured = false 
    } = req.query;

    const offset = (page - 1) * limit;
    let whereClause = "WHERE a.status = 'active'";
    const queryParams = [];
    let paramCount = 0;

    if (category) {
      paramCount++;
      whereClause += ` AND a.category = $${paramCount}`;
      queryParams.push(category);
    }

    if (search) {
      paramCount++;
      whereClause += ` AND (a.name ILIKE $${paramCount} OR a.description ILIKE $${paramCount})`;
      queryParams.push(`%${search}%`);
    }

    if (featured === 'true') {
      whereClause += ` AND a.is_official = true`;
    }

    const validSortFields = ['name', 'rating', 'deployments', 'created_at', 'price'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';
    const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const query = `
      SELECT 
        a.*,
        u.username as creator_username,
        COUNT(*) OVER() as total_count
      FROM agents a
      LEFT JOIN users u ON a.creator_id = u.id
      ${whereClause}
      ORDER BY a.${sortField} ${sortOrder}
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    queryParams.push(limit, offset);
    const result = await pool.query(query, queryParams);

    const totalCount = result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0;
    const totalPages = Math.ceil(totalCount / limit);

    // Remove total_count from each row
    const agents = result.rows.map(row => {
      const { total_count, ...agent } = row;
      return agent;
    });

    res.json({
      success: true,
      data: {
        agents,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get featured agents
exports.getFeaturedAgents = async (req, res, next) => {
  try {
    const query = `
      SELECT 
        a.*,
        u.username as creator_username
      FROM agents a
      LEFT JOIN users u ON a.creator_id = u.id
      WHERE a.status = 'active' AND a.is_official = true
      ORDER BY a.rating DESC, a.deployments DESC
      LIMIT 12
    `;

    const result = await pool.query(query);

    res.json({
      success: true,
      data: {
        agents: result.rows
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get agent by slug
exports.getAgentBySlug = async (req, res, next) => {
  try {
    const { slug } = req.params;

    const query = `
      SELECT 
        a.*,
        u.username as creator_username
      FROM agents a
      LEFT JOIN users u ON a.creator_id = u.id
      WHERE a.slug = $1 AND a.status = 'active'
    `;

    const result = await pool.query(query, [slug]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent not found'
        }
      });
    }

    // Increment deployment count (view)
    await pool.query(
      'UPDATE agents SET deployments = deployments + 1 WHERE id = $1',
      [result.rows[0].id]
    );

    res.json({
      success: true,
      data: {
        agent: result.rows[0]
      }
    });
  } catch (error) {
    next(error);
  }
};

// Create new agent
exports.createAgent = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const {
      name,
      description,
      category,
      image_url,
      price = 0,
      technical_specs = {}
    } = req.body;

    // Generate slug from name
    let slug = slugify(name, { lower: true, strict: true });
    
    // Check if slug exists and make it unique
    let slugExists = true;
    let counter = 0;
    let uniqueSlug = slug;

    while (slugExists) {
      const slugCheck = await pool.query(
        'SELECT id FROM agents WHERE slug = $1',
        [uniqueSlug]
      );

      if (slugCheck.rows.length === 0) {
        slugExists = false;
      } else {
        counter++;
        uniqueSlug = `${slug}-${counter}`;
      }
    }

    // Get user username
    const userResult = await pool.query(
      'SELECT username FROM users WHERE id = $1',
      [userId]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        }
      });
    }

    const username = userResult.rows[0].username;

    // Create agent
    const query = `
      INSERT INTO agents (
        name, slug, description, category, image_url, price,
        creator_id, creator_username, technical_specs, status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `;

    const values = [
      name,
      uniqueSlug,
      description,
      category,
      image_url,
      price,
      userId,
      username,
      JSON.stringify(technical_specs),
      'pending' // New agents need approval
    ];

    const result = await pool.query(query, values);

    // Create user profile if it doesn't exist
    await pool.query(`
      INSERT INTO user_profiles (user_id, username, avatar_url)
      SELECT $1, $2, $3
      WHERE NOT EXISTS (SELECT 1 FROM user_profiles WHERE user_id = $1)
    `, [userId, username, userResult.rows[0].avatar_url]);

    res.status(201).json({
      success: true,
      data: {
        agent: result.rows[0]
      }
    });
  } catch (error) {
    next(error);
  }
};

// Update agent
exports.updateAgent = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const updates = req.body;

    // Check if agent exists and user owns it
    const agentResult = await pool.query(
      'SELECT * FROM agents WHERE id = $1',
      [id]
    );

    if (agentResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent not found'
        }
      });
    }

    const agent = agentResult.rows[0];

    if (agent.creator_id !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only update your own agents'
        }
      });
    }

    // Build update query
    const allowedUpdates = ['name', 'description', 'category', 'image_url', 'price', 'technical_specs'];
    const updateFields = [];
    const updateValues = [];
    let paramCount = 0;

    Object.keys(updates).forEach(key => {
      if (allowedUpdates.includes(key)) {
        paramCount++;
        updateFields.push(`${key} = $${paramCount}`);
        updateValues.push(key === 'technical_specs' ? JSON.stringify(updates[key]) : updates[key]);
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'NO_VALID_UPDATES',
          message: 'No valid fields to update'
        }
      });
    }

    // Add updated_at
    paramCount++;
    updateFields.push(`updated_at = $${paramCount}`);
    updateValues.push(new Date());

    // Add WHERE clause
    paramCount++;
    updateValues.push(id);

    const query = `
      UPDATE agents 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await pool.query(query, updateValues);

    res.json({
      success: true,
      data: {
        agent: result.rows[0]
      }
    });
  } catch (error) {
    next(error);
  }
};

// Delete agent
exports.deleteAgent = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Check if agent exists and user owns it
    const agentResult = await pool.query(
      'SELECT * FROM agents WHERE id = $1',
      [id]
    );

    if (agentResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent not found'
        }
      });
    }

    const agent = agentResult.rows[0];

    if (agent.creator_id !== userId) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'You can only delete your own agents'
        }
      });
    }

    // Soft delete by updating status
    await pool.query(
      "UPDATE agents SET status = 'deleted', updated_at = CURRENT_TIMESTAMP WHERE id = $1",
      [id]
    );

    res.json({
      success: true,
      message: 'Agent deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Get agent reviews
exports.getAgentReviews = async (req, res, next) => {
  try {
    const { slug } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // First get agent by slug
    const agentResult = await pool.query(
      'SELECT id FROM agents WHERE slug = $1',
      [slug]
    );

    if (agentResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent not found'
        }
      });
    }

    const agentId = agentResult.rows[0].id;

    // Get reviews with user info
    const query = `
      SELECT 
        r.*,
        u.username,
        u.avatar_url,
        COUNT(*) OVER() as total_count
      FROM agent_reviews r
      LEFT JOIN users u ON r.user_id = u.id
      WHERE r.agent_id = $1
      ORDER BY r.created_at DESC
      LIMIT $2 OFFSET $3
    `;

    const result = await pool.query(query, [agentId, limit, offset]);

    const totalCount = result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0;
    const totalPages = Math.ceil(totalCount / limit);

    // Remove total_count from each row
    const reviews = result.rows.map(row => {
      const { total_count, ...review } = row;
      return review;
    });

    res.json({
      success: true,
      data: {
        reviews,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

// Create agent review
exports.createAgentReview = async (req, res, next) => {
  try {
    const { slug } = req.params;
    const userId = req.user.id;
    const { rating, comment } = req.body;

    // Get agent by slug
    const agentResult = await pool.query(
      'SELECT id FROM agents WHERE slug = $1',
      [slug]
    );

    if (agentResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'AGENT_NOT_FOUND',
          message: 'Agent not found'
        }
      });
    }

    const agentId = agentResult.rows[0].id;

    // Check if user already reviewed this agent
    const existingReview = await pool.query(
      'SELECT id FROM agent_reviews WHERE agent_id = $1 AND user_id = $2',
      [agentId, userId]
    );

    if (existingReview.rows.length > 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'REVIEW_EXISTS',
          message: 'You have already reviewed this agent'
        }
      });
    }

    // Create review
    const insertQuery = `
      INSERT INTO agent_reviews (agent_id, user_id, rating, comment)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;

    const result = await pool.query(insertQuery, [agentId, userId, rating, comment]);

    // Update agent rating
    const ratingQuery = `
      UPDATE agents 
      SET rating = (
        SELECT ROUND(AVG(rating)::numeric, 2) 
        FROM agent_reviews 
        WHERE agent_id = $1
      )
      WHERE id = $1
    `;

    await pool.query(ratingQuery, [agentId]);

    res.status(201).json({
      success: true,
      data: {
        review: result.rows[0]
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get rankings
exports.getRankings = async (req, res, next) => {
  try {
    const { sortBy = 'rating', limit = 50 } = req.query;

    const validSortFields = {
      'rating': 'rating DESC',
      'deployments': 'deployments DESC',
      'newest': 'created_at DESC'
    };

    const orderBy = validSortFields[sortBy] || 'rating DESC';

    const query = `
      SELECT 
        a.*,
        u.username as creator_username,
        ROW_NUMBER() OVER (ORDER BY ${orderBy}) as rank
      FROM agents a
      LEFT JOIN users u ON a.creator_id = u.id
      WHERE a.status = 'active'
      ORDER BY ${orderBy}
      LIMIT $1
    `;

    const result = await pool.query(query, [limit]);

    res.json({
      success: true,
      data: {
        rankings: result.rows
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get categories with agent counts
exports.getCategories = async (req, res, next) => {
  try {
    const query = `
      SELECT 
        c.*,
        COUNT(a.id) as count
      FROM categories c
      LEFT JOIN agents a ON c.name = a.category AND a.status = 'active'
      GROUP BY c.id, c.name, c.description, c.icon, c.created_at
      ORDER BY count DESC, c.name ASC
    `;

    const result = await pool.query(query);

    res.json({
      success: true,
      data: {
        categories: result.rows
      }
    });
  } catch (error) {
    next(error);
  }
};