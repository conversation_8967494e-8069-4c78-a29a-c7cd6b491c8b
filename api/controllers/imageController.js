const axios = require('axios');
const https = require('https');
const FormData = require('form-data');

const { STABILITY_API_KEY } = process.env;

const httpsAgent = new https.Agent({ keepAlive: true });
const axiosInstance = axios.create({ httpsAgent });

exports.generateHfImage = async (req, res, next) => {
  if (!STABILITY_API_KEY) {
    // We explicitly check for the key here instead of crashing the server on start
    return res.status(501).json({ error: 'Image generation service is not configured on the server.' });
  }

  try {
    const prompt = req.body.inputs; // Already validated by Zod middleware

    const formData = new FormData();
    formData.append('prompt', prompt);
    formData.append('output_format', 'png');

    const response = await axiosInstance.post(
      'https://api.stability.ai/v2beta/stable-image/generate/core',
      formData,
      {
        headers: {
          ...formData.getHeaders(),
          Authorization: `Bearer ${STABILITY_API_KEY}`,
          Accept: 'image/*',
        },
        responseType: 'arraybuffer',
      }
    );

    res.set('Content-Type', 'image/png');
    res.send(response.data);
  } catch (err) {
    next(err); // Pass error to the centralized error handler
  }
}; 