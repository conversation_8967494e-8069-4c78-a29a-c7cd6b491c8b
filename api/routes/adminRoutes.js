const express = require('express');
const router = express.Router();
const { verifyToken, requireAdmin } = require('../../lib/auth');
const {
  getDashboardStats,
  getUsers,
  updateUser,
  getAgentsAdmin,
  updateAgentStatus,
  getUsageStats
} = require('../controllers/adminController');

// All admin routes require authentication and admin role
router.use(verifyToken);
router.use(requireAdmin);

// Dashboard statistics
router.get('/dashboard', getDashboardStats);

// User management
router.get('/users', getUsers);
router.put('/users/:userId', updateUser);

// Agent management
router.get('/agents', getAgentsAdmin);
router.put('/agents/:agentId/status', updateAgentStatus);

// Usage statistics
router.get('/usage-stats', getUsageStats);

module.exports = router;
