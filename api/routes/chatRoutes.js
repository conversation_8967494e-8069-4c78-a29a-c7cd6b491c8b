const express = require('express');
const router = express.Router();
const { verifyToken } = require('../../lib/auth');
const {
  getMessages,
  sendMessage,
  saveBotResponse,
  clearMessages,
  updateInteractionHistory
} = require('../controllers/chatController');

// All chat routes require authentication
router.get('/messages', verifyToken, getMessages);
router.post('/messages', verifyToken, sendMessage);
router.post('/messages/bot-response', verifyToken, saveBotResponse);
router.delete('/messages', verifyToken, clearMessages);
router.post('/interaction-history', verifyToken, updateInteractionHistory);

module.exports = router; 