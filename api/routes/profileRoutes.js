const express = require('express');
const router = express.Router();
const { verifyToken, optionalAuth } = require('../../lib/auth');
const {
  getProfile,
  getSettings,
  updateSettings,
  updateProfile,
  getBookmarks,
  addBookmark,
  removeBookmark,
  getHistory,
  clearHistory,
  getDashboardStats,
  getCreatedAgents
} = require('../controllers/profileController');

// Public routes
router.get('/profile/:username', getProfile);
router.get('/created-agents/:userId', getCreatedAgents);

// Protected routes
router.get('/settings', verifyToken, getSettings);
router.put('/settings', verifyToken, updateSettings);
router.put('/profile', verifyToken, updateProfile);

router.get('/bookmarks', verifyToken, getBookmarks);
router.post('/bookmarks', verifyToken, addBookmark);
router.delete('/bookmarks/:bookmarkId', verifyToken, removeBookmark);

router.get('/history', verifyToken, getHistory);
router.delete('/history', verifyToken, clearHistory);

router.get('/dashboard-stats', verifyToken, getDashboardStats);

module.exports = router; 