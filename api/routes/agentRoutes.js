const express = require('express');
const router = express.Router();
const { verifyToken, optionalAuth } = require('../../lib/auth');
const {
  getAgents,
  getAgentBySlug,
  createAgent,
  updateAgent,
  getAgentReviews,
  addAgentReview,
  getCategories,
  getRankings,
  getFeaturedAgents
} = require('../controllers/agentController');

// Public routes
router.get('/agents', getAgents);
router.get('/agents/featured', getFeaturedAgents);
router.get('/agents/:slug', getAgentBySlug);
router.get('/agents/:agentId/reviews', getAgentReviews);
router.get('/categories', getCategories);
router.get('/rankings', getRankings);

// Protected routes
router.post('/agents', verifyToken, createAgent);
router.put('/agents/:agentId', verifyToken, updateAgent);
router.post('/agents/:agentId/reviews', verifyToken, addAgentReview);

module.exports = router; 