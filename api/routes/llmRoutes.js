const express = require('express');
const router = express.Router();
const { verifyToken, optionalAuth } = require('../../lib/auth');
const { validate } = require('../middleware/validate');
const { reportLogger, usageLogger } = require('../middleware/loggers');
const { trackUsage, checkCredits } = require('../middleware/usageTracker');
const {
  queryLLM,
  queryGemini,
  queryGPT4,
  queryClaude,
  queryDeepSeek,
  queryGrok,
  getModels,
  healthCheck
} = require('../controllers/llmController');
const {
  llmQuerySchema,
  llmPromptSchema
} = require('../validators/llmValidators');

// Apply usage logging to all routes
router.use(usageLogger);
router.use(reportLogger);

// Health check (public)
router.get('/health', healthCheck);

// Get available models (public)
router.get('/models', getModels);

// Main LLM query endpoint
router.post('/query', optionalAuth, checkCredits(0.001), validate(llmQuerySchema), trackUsage('llm'), queryLLM);

// Legacy endpoints for backward compatibility
router.post('/gemini', optionalAuth, checkCredits(0.001), validate(llmPromptSchema), trackUsage('llm'), queryGemini);
router.post('/deepseek', optionalAuth, checkCredits(0.001), validate(llmPromptSchema), trackUsage('llm'), queryDeepSeek);
router.post('/grok2', optionalAuth, checkCredits(0.001), validate(llmPromptSchema), trackUsage('llm'), queryGrok);
router.post('/claude', optionalAuth, checkCredits(0.001), validate(llmPromptSchema), trackUsage('llm'), queryClaude);
router.post('/gpt4', optionalAuth, checkCredits(0.001), validate(llmPromptSchema), trackUsage('llm'), queryGPT4);

// Alternative paths for compatibility
router.post('/openai', optionalAuth, validate(llmPromptSchema), queryGPT4);
router.post('/anthropic', optionalAuth, validate(llmPromptSchema), queryClaude);
router.post('/xai', optionalAuth, validate(llmPromptSchema), queryGrok);

module.exports = router; 