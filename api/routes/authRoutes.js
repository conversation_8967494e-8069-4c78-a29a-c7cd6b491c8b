const express = require('express');
const router = express.Router();
const { verifyToken } = require('../../lib/auth');
const { validate } = require('../middleware/validate');
const {
  signUpSchema,
  signInSchema,
  googleAuthSchema,
  walletVerifySchema
} = require('../validators/authValidators');
const {
  signUp,
  signIn,
  googleAuth,
  generateNonce,
  verifyWallet,
  getCurrentUser
} = require('../controllers/authController');

// Public auth routes
router.post('/signup', validate(signUpSchema), signUp);
router.post('/signin', validate(signInSchema), signIn);
router.post('/google', validate(googleAuthSchema), googleAuth);

// Wallet authentication
router.post('/nonce', generateNonce);
router.post('/verify', validate(walletVerifySchema), verifyWallet);

// Protected routes
router.get('/user', verifyToken, getCurrentUser);

// Sign out (client-side token removal)
router.post('/signout', (req, res) => {
  res.json({ 
    success: true, 
    message: 'Signed out successfully' 
  });
});

module.exports = router; 