const express = require('express');
const router = express.Router();
const { verifyToken } = require('../../lib/auth');
const { validate } = require('../middleware/validate');
const {
  getBalance,
  createPayment,
  confirmPayment,
  getPaymentHistory,
  getUserCredits
} = require('../controllers/paymentController');
const {
  createPaymentSchema,
  confirmPaymentSchema
} = require('../validators/paymentValidators');

// All payment routes require authentication
router.use(verifyToken);

// Get user's dGPU balance
router.get('/balance', getBalance);

// Get user credits
router.get('/credits', getUserCredits);

// Get payment history
router.get('/history', getPaymentHistory);

// Create payment transaction
router.post('/create', validate(createPaymentSchema), createPayment);

// Confirm payment transaction
router.post('/confirm', validate(confirmPaymentSchema), confirmPayment);

module.exports = router;
