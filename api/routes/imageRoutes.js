const express = require('express');
const router = express.Router();
const { verifyToken, optionalAuth } = require('../../lib/auth');
const { validate } = require('../middleware/validate');
const { reportLogger, usageLogger } = require('../middleware/loggers');
const {
  generateHfImage
} = require('../controllers/imageController');
const {
  imageGenerationSchema,
  imageUploadSchema
} = require('../validators/imageValidators');

// Apply usage logging to all routes
router.use(usageLogger);
router.use(reportLogger);

// Image generation endpoints
router.post('/generate', optionalAuth, validate(imageGenerationSchema), generateHfImage);

// Alternative endpoint names for compatibility
router.post('/hf-image', optionalAuth, validate(imageGenerationSchema), generateHfImage);
router.post('/stability', optionalAuth, validate(imageGenerationSchema), generateHfImage);

module.exports = router; 