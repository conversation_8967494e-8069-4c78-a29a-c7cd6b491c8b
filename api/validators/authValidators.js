const { z } = require('zod');

// Sign up validation schema
const signUpSchema = z.object({
  body: z.object({
    email: z.string()
      .email('Please provide a valid email address')
      .max(255, 'Email must be less than 255 characters'),
    password: z.string()
      .min(6, 'Password must be at least 6 characters long')
      .max(128, 'Password must be less than 128 characters'),
    username: z.string()
      .min(3, 'Username must be at least 3 characters long')
      .max(30, 'Username must be no more than 30 characters')
      .regex(/^[a-z0-9_-]+$/, 'Username can only contain lowercase letters, numbers, underscores, and hyphens')
  })
});

// Sign in validation schema
const signInSchema = z.object({
  body: z.object({
    email: z.string()
      .email('Please provide a valid email address'),
    password: z.string()
      .min(1, 'Password is required')
  })
});

// Google OAuth validation schema
const googleAuthSchema = z.object({
  body: z.object({
    idToken: z.string()
      .min(1, 'Google ID token is required')
  })
});

// Wallet verification validation schema
const walletVerifySchema = z.object({
  body: z.object({
    publicKey: z.string()
      .min(1, 'Public key is required'),
    signature: z.array(z.number())
      .min(1, 'Signature is required'),
    nonce: z.string()
      .min(1, 'Nonce is required')
  })
});

// Password reset request schema
const passwordResetRequestSchema = z.object({
  body: z.object({
    email: z.string()
      .email('Invalid email format')
      .min(1, 'Email is required')
  })
});

// Password reset schema
const passwordResetSchema = z.object({
  body: z.object({
    token: z.string()
      .min(1, 'Reset token is required'),
    password: z.string()
      .min(6, 'Password must be at least 6 characters long')
      .max(100, 'Password is too long')
  })
});

// Change password schema
const changePasswordSchema = z.object({
  body: z.object({
    currentPassword: z.string()
      .min(1, 'Current password is required'),
    newPassword: z.string()
      .min(6, 'New password must be at least 6 characters long')
      .max(100, 'New password is too long')
  })
});

module.exports = {
  signUpSchema,
  signInSchema,
  googleAuthSchema,
  walletVerifySchema,
  passwordResetRequestSchema,
  passwordResetSchema,
  changePasswordSchema
};
