const { z } = require('zod');

// Send message validation schema
const sendMessageSchema = z.object({
  body: z.object({
    content: z.string().min(1, 'Message content is required').max(10000, 'Message too long'),
    is_markdown: z.boolean().optional(),
    agent_id: z.string().uuid('Invalid agent ID').optional()
  })
});

// Save bot response validation schema
const saveBotResponseSchema = z.object({
  body: z.object({
    content: z.string().min(1, 'Response content is required').max(50000, 'Response too long'),
    is_markdown: z.boolean().optional(),
    agent_id: z.string().uuid('Invalid agent ID').optional()
  })
});

// Get messages validation schema
const getMessagesSchema = z.object({
  query: z.object({
    agentId: z.string().uuid('Invalid agent ID').optional(),
    limit: z.string().regex(/^\d+$/, 'Limit must be a number').optional(),
    offset: z.string().regex(/^\d+$/, 'Offset must be a number').optional()
  })
});

// Update interaction history validation schema
const updateInteractionHistorySchema = z.object({
  body: z.object({
    agent_id: z.string().uuid('Invalid agent ID'),
    duration_ms: z.number().int().min(0, 'Duration must be non-negative')
  })
});

module.exports = {
  sendMessageSchema,
  saveBotResponseSchema,
  getMessagesSchema,
  updateInteractionHistorySchema
};
