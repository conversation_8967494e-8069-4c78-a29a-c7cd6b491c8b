const { z } = require('zod');

// Agent creation validation schema
const createAgentSchema = z.object({
  body: z.object({
    name: z.string().min(1, 'Agent name is required').max(255, 'Agent name too long'),
    description: z.string().min(10, 'Description must be at least 10 characters').max(2000, 'Description too long'),
    category: z.string().min(1, 'Category is required'),
    image_url: z.string().url('Invalid image URL').optional(),
    price: z.number().min(0, 'Price must be non-negative').optional(),
    technical_specs: z.object({}).optional()
  })
});

// Agent update validation schema
const updateAgentSchema = z.object({
  params: z.object({
    agentId: z.string().uuid('Invalid agent ID')
  }),
  body: z.object({
    name: z.string().min(1, 'Agent name is required').max(255, 'Agent name too long').optional(),
    description: z.string().min(10, 'Description must be at least 10 characters').max(2000, 'Description too long').optional(),
    category: z.string().min(1, 'Category is required').optional(),
    image_url: z.string().url('Invalid image URL').optional(),
    price: z.number().min(0, 'Price must be non-negative').optional(),
    technical_specs: z.object({}).optional(),
    status: z.enum(['active', 'inactive', 'pending', 'deleted']).optional()
  })
});

// Agent review validation schema
const addReviewSchema = z.object({
  params: z.object({
    agentId: z.string().uuid('Invalid agent ID')
  }),
  body: z.object({
    rating: z.number().int().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5'),
    comment: z.string().max(1000, 'Comment too long').optional()
  })
});

// Agent query validation schema
const getAgentsSchema = z.object({
  query: z.object({
    category: z.string().optional(),
    search: z.string().optional(),
    sortBy: z.enum(['created_at', 'rating', 'deployments', 'name']).optional(),
    order: z.enum(['ASC', 'DESC']).optional(),
    page: z.string().regex(/^\d+$/, 'Page must be a number').optional(),
    limit: z.string().regex(/^\d+$/, 'Limit must be a number').optional(),
    featured: z.enum(['true', 'false']).optional()
  })
});

// Agent slug validation schema
const getAgentBySlugSchema = z.object({
  params: z.object({
    slug: z.string().min(1, 'Slug is required')
  })
});

module.exports = {
  createAgentSchema,
  updateAgentSchema,
  addReviewSchema,
  getAgentsSchema,
  getAgentBySlugSchema
};
