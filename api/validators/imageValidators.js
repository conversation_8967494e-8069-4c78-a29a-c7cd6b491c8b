const { z } = require('zod');

// Image generation schema
const imageGenerationSchema = z.object({
  body: z.object({
    inputs: z.string()
      .min(1, 'Prompt is required')
      .max(2000, 'Prompt is too long'),
    prompt: z.string()
      .min(1, 'Prompt is required')
      .max(2000, 'Prompt is too long')
      .optional(),
    width: z.number()
      .min(256)
      .max(2048)
      .optional()
      .default(1024),
    height: z.number()
      .min(256)
      .max(2048)
      .optional()
      .default(1024),
    steps: z.number()
      .min(1)
      .max(50)
      .optional()
      .default(20),
    guidance_scale: z.number()
      .min(1)
      .max(20)
      .optional()
      .default(7.5),
    negative_prompt: z.string()
      .max(1000, 'Negative prompt is too long')
      .optional(),
    output_format: z.enum(['png', 'jpeg', 'webp'])
      .optional()
      .default('png'),
    seed: z.number()
      .int()
      .min(0)
      .optional(),
    model: z.enum([
      'stable-diffusion-xl',
      'stable-diffusion-2-1',
      'dalle-3',
      'midjourney-v6'
    ]).optional().default('stable-diffusion-xl')
  }).refine(
    (data) => data.inputs || data.prompt,
    {
      message: 'Either inputs or prompt is required',
      path: ['inputs']
    }
  )
});

// Image upload schema
const imageUploadSchema = z.object({
  body: z.object({
    image: z.string()
      .min(1, 'Image data is required'),
    format: z.enum(['png', 'jpeg', 'webp'])
      .optional()
      .default('png'),
    quality: z.number()
      .min(1)
      .max(100)
      .optional()
      .default(90),
    resize: z.object({
      width: z.number().min(1).max(4096),
      height: z.number().min(1).max(4096)
    }).optional()
  })
});

// Image editing schema
const imageEditSchema = z.object({
  body: z.object({
    image: z.string()
      .min(1, 'Original image is required'),
    prompt: z.string()
      .min(1, 'Edit prompt is required')
      .max(2000, 'Prompt is too long'),
    mask: z.string().optional(),
    strength: z.number()
      .min(0)
      .max(1)
      .optional()
      .default(0.8),
    guidance_scale: z.number()
      .min(1)
      .max(20)
      .optional()
      .default(7.5),
    steps: z.number()
      .min(1)
      .max(50)
      .optional()
      .default(20)
  })
});

// Batch image generation schema
const batchImageSchema = z.object({
  body: z.object({
    prompts: z.array(z.string().min(1).max(2000))
      .min(1, 'At least one prompt is required')
      .max(10, 'Maximum 10 prompts per batch'),
    model: z.string().optional().default('stable-diffusion-xl'),
    width: z.number().min(256).max(2048).optional().default(1024),
    height: z.number().min(256).max(2048).optional().default(1024),
    steps: z.number().min(1).max(50).optional().default(20),
    guidance_scale: z.number().min(1).max(20).optional().default(7.5)
  })
});

module.exports = {
  imageGenerationSchema,
  imageUploadSchema,
  imageEditSchema,
  batchImageSchema
}; 