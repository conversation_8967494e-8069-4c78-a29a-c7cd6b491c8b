const { z } = require('zod');

// Create payment validation schema
const createPaymentSchema = z.object({
  body: z.object({
    agent_id: z.string().uuid('Invalid agent ID'),
    amount: z.number().positive('Amount must be positive'),
    usage_type: z.enum(['chat', 'generation', 'analysis', 'subscription']).optional()
  })
});

// Confirm payment validation schema
const confirmPaymentSchema = z.object({
  body: z.object({
    payment_id: z.string().uuid('Invalid payment ID'),
    transaction_signature: z.string().min(1, 'Transaction signature is required')
  })
});

// Get payment history validation schema
const getPaymentHistorySchema = z.object({
  query: z.object({
    page: z.string().regex(/^\d+$/, 'Page must be a number').optional(),
    limit: z.string().regex(/^\d+$/, 'Limit must be a number').optional(),
    status: z.enum(['pending', 'confirmed', 'failed']).optional(),
    agent_id: z.string().uuid('Invalid agent ID').optional()
  })
});

module.exports = {
  createPaymentSchema,
  confirmPaymentSchema,
  getPaymentHistorySchema
};
