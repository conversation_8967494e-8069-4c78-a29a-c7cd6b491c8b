const { z } = require('zod');

// Update profile validation schema
const updateProfileSchema = z.object({
  body: z.object({
    username: z.string().min(3, 'Username must be at least 3 characters').max(50, 'Username too long').optional(),
    bio: z.string().max(500, 'Bio too long').optional(),
    website: z.string().url('Invalid website URL').optional(),
    avatar_url: z.string().url('Invalid avatar URL').optional(),
    wallet_address: z.string().min(32, 'Invalid wallet address').max(44, 'Invalid wallet address').optional()
  })
});

// Get profile validation schema
const getProfileSchema = z.object({
  params: z.object({
    username: z.string().min(1, 'Username is required')
  })
});

// Update settings validation schema
const updateSettingsSchema = z.object({
  body: z.object({
    theme: z.enum(['light', 'dark']).optional(),
    show_email: z.boolean().optional(),
    bio: z.string().max(500, 'Bio too long').optional(),
    avatar_url: z.string().url('Invalid avatar URL').optional(),
    website: z.string().url('Invalid website URL').optional(),
    social_links: z.object({
      twitter: z.string().url('Invalid Twitter URL').optional(),
      github: z.string().url('Invalid GitHub URL').optional(),
      linkedin: z.string().url('Invalid LinkedIn URL').optional(),
      discord: z.string().optional()
    }).optional(),
    notification_preferences: z.object({
      email: z.boolean().optional(),
      push: z.boolean().optional(),
      marketing: z.boolean().optional()
    }).optional()
  })
});

// Add bookmark validation schema
const addBookmarkSchema = z.object({
  body: z.object({
    agent_id: z.string().uuid('Invalid agent ID'),
    notes: z.string().max(1000, 'Notes too long').optional()
  })
});

// Remove bookmark validation schema
const removeBookmarkSchema = z.object({
  params: z.object({
    bookmarkId: z.string().uuid('Invalid bookmark ID')
  })
});

module.exports = {
  updateProfileSchema,
  getProfileSchema,
  updateSettingsSchema,
  addBookmarkSchema,
  removeBookmarkSchema
};
