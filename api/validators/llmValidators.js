const { z } = require('zod');

// Basic prompt validation schema
const llmPromptSchema = z.object({
  body: z.object({
    prompt: z.string()
      .min(1, 'Prompt is required')
      .max(50000, 'Prompt is too long'),
    systemPrompt: z.string().optional(),
    temperature: z.number()
      .min(0)
      .max(2)
      .optional()
      .default(0.7),
    max_tokens: z.number()
      .min(1)
      .max(32000)
      .optional()
  })
});

// Advanced LLM query schema with model selection
const llmQuerySchema = z.object({
  body: z.object({
    prompt: z.string()
      .min(1, 'Prompt is required')
      .max(50000, 'Prompt is too long'),
    model: z.enum([
      'gemini-1-5-pro',
      'gemini-2-0-flash', 
      'gpt-4o',
      'claude-3-7-sonnet',
      'deepseek-v3-fw',
      'grok-2'
    ]).optional().default('gemini-1-5-pro'),
    temperature: z.number()
      .min(0)
      .max(2)
      .optional()
      .default(0.7),
    max_tokens: z.number()
      .min(1)
      .max(32000)
      .optional(),
    stream: z.boolean().optional().default(false),
    context: z.string().optional(),
    user_id: z.string().optional()
  })
});

// Legacy unified endpoint schema
const llmUnifiedSchema = z.object({
  body: z.object({
    slug: z.string()
      .min(1, 'Agent slug is required'),
    prompt: z.string()
      .min(1, 'Prompt is required')
      .max(50000, 'Prompt is too long'),
    systemPrompt: z.string().optional(),
    temperature: z.number()
      .min(0)
      .max(2)
      .optional()
  })
});

// Chat completion schema
const chatCompletionSchema = z.object({
  body: z.object({
    messages: z.array(
      z.object({
        role: z.enum(['system', 'user', 'assistant']),
        content: z.string().min(1)
      })
    ).min(1, 'At least one message is required'),
    model: z.string().optional().default('gemini-1-5-pro'),
    temperature: z.number()
      .min(0)
      .max(2)
      .optional()
      .default(0.7),
    max_tokens: z.number()
      .min(1)
      .max(32000)
      .optional(),
    stream: z.boolean().optional().default(false)
  })
});

// Batch processing schema
const batchProcessSchema = z.object({
  body: z.object({
    requests: z.array(
      z.object({
        id: z.string().optional(),
        prompt: z.string().min(1),
        model: z.string().optional()
      })
    ).min(1).max(100, 'Maximum 100 requests per batch'),
    model: z.string().optional().default('gemini-1-5-pro'),
    temperature: z.number()
      .min(0)
      .max(2)
      .optional()
      .default(0.7)
  })
});

module.exports = {
  llmPromptSchema,
  llmQuerySchema,
  llmUnifiedSchema,
  chatCompletionSchema,
  batchProcessSchema
}; 