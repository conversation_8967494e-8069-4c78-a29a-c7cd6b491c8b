-- Agora Marketplace Database Setup
-- Run this script to create the database and populate it with sample data

-- Create the database if it doesn't exist
-- Run this part separately as a superuser:
-- CREATE DATABASE agora_marketplace;
-- CREATE USER agora_user WITH PASSWORD 'agora_password';
-- GRANT ALL PRIVILEGES ON DATABASE agora_marketplace TO agora_user;

-- Connect to the agora_marketplace database and run the schema
\c agora_marketplace;

-- Run the main schema
\i schema.sql;

-- Insert sample data for development
-- Create admin user first
INSERT INTO users (email, password_hash, role, email_verified) VALUES 
('<EMAIL>', '$2b$12$LQv3c1yqBwfnYdPgdGJThO.Lks0r5Hv1QGFMlmfuQl2kJNOW1KQ5W', 'admin', true)
ON CONFLICT (email) DO NOTHING;

-- Get admin user ID for later use
DO $$
DECLARE
    admin_user_id UUID;
BEGIN
    SELECT id INTO admin_user_id FROM users WHERE email = '<EMAIL>';
    
    -- Create admin user profile
    INSERT INTO user_profiles (user_id, username, email) VALUES 
    (admin_user_id, 'admin', '<EMAIL>')
    ON CONFLICT (user_id) DO NOTHING;
    
    -- Insert sample agents with proper references
    INSERT INTO agents (name, slug, description, category, creator_id, creator_username, image_url, is_official) VALUES
    ('Gemini Pro', 'gemini-1-5-pro', 'Google''s advanced language model for complex reasoning and analysis', 'AI Assistant', admin_user_id, 'admin', 'https://api.dicebear.com/8.x/bottts/svg?seed=gemini', true),
    ('Claude Sonnet', 'claude-3-7-sonnet', 'Anthropic''s powerful AI assistant for thoughtful conversations', 'AI Assistant', admin_user_id, 'admin', 'https://api.dicebear.com/8.x/bottts/svg?seed=claude', true),
    ('GPT-4', 'gpt-4o', 'OpenAI''s flagship language model', 'AI Assistant', admin_user_id, 'admin', 'https://api.dicebear.com/8.x/bottts/svg?seed=gpt4', true),
    ('DeepSeek Coder', 'deepseek-v3-fw', 'Specialized coding assistant', 'Code Assistant', admin_user_id, 'admin', 'https://api.dicebear.com/8.x/bottts/svg?seed=deepseek', true),
    ('Image Generator', 'hf-image', 'AI-powered image generation tool', 'Image Generation', admin_user_id, 'admin', 'https://api.dicebear.com/8.x/bottts/svg?seed=image', true),
    ('Article Writer', 'article-writer-agent', 'Professional content and article writing assistant', 'Writing', admin_user_id, 'admin', 'https://api.dicebear.com/8.x/bottts/svg?seed=writer', true),
    ('Audit Analyst', 'audit-analys-agent', 'Smart contract and code security auditing tool', 'Audit', admin_user_id, 'admin', 'https://api.dicebear.com/8.x/bottts/svg?seed=audit', true),
    ('Tokenomics Analyst', 'tokenomics-analys-agent', 'Blockchain tokenomics analysis and consulting', 'Tokenomics', admin_user_id, 'admin', 'https://api.dicebear.com/8.x/bottts/svg?seed=tokenomics', true),
    ('Grok 2', 'grok-2', 'X.AI''s conversational AI with real-time knowledge', 'AI Assistant', admin_user_id, 'admin', 'https://api.dicebear.com/8.x/bottts/svg?seed=grok', true),
    ('Gemini Flash', 'gemini-2-0-flash', 'Google''s fast and efficient AI model', 'AI Assistant', admin_user_id, 'admin', 'https://api.dicebear.com/8.x/bottts/svg?seed=flash', true)
    ON CONFLICT (slug) DO NOTHING;
END $$;

-- Create sample discussions
INSERT INTO discussions (title, content, user_id, is_markdown) VALUES
('Welcome to Agora Marketplace!', 'Welcome to our AI agent marketplace. Feel free to discuss anything related to AI, agents, or the platform here.', (SELECT id FROM users WHERE email = '<EMAIL>'), true),
('Best Practices for AI Agent Development', 'What are your tips for creating effective AI agents? Share your experiences and best practices here.', (SELECT id FROM users WHERE email = '<EMAIL>'), true),
('Feature Requests and Feedback', 'Have ideas for improving the platform? Post your feature requests and feedback here.', (SELECT id FROM users WHERE email = '<EMAIL>'), true)
ON CONFLICT DO NOTHING;

-- Grant permissions to agora_user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO agora_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO agora_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO agora_user;

PRINT 'Database setup completed successfully!'; 