-- Agora Marketplace Database Schema
-- Complete PostgreSQL setup for local development

-- Drop existing tables if they exist (in correct order due to foreign keys)
DROP TABLE IF EXISTS tool_reports CASCADE;
DROP TABLE IF EXISTS usage_logs CASCADE;
DROP TABLE IF EXISTS discussion_likes CASCADE;
DROP TABLE IF EXISTS reply_likes CASCADE;
DROP TABLE IF EXISTS discussion_replies CASCADE;
DROP TABLE IF EXISTS discussions CASCADE;
DROP TABLE IF EXISTS content_reports CASCADE;
DROP TABLE IF EXISTS moderation_actions CASCADE;
DROP TABLE IF EXISTS user_bans CASCADE;
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS user_activity CASCADE;
DROP TABLE IF EXISTS bot_analytics CASCADE;
DROP TABLE IF EXISTS chat_messages CASCADE;
DROP TABLE IF EXISTS user_agent_history CASCADE;
DROP TABLE IF EXISTS agent_bookmarks CASCADE;
DROP TABLE IF EXISTS user_settings CASCADE;
DROP TABLE IF EXISTS agent_reviews CASCADE;
DROP TABLE IF EXISTS agents CASCADE;
DROP TABLE IF EXISTS user_profiles CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS categories CASCADE;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create users table for authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    google_id VARCHAR(255) UNIQUE,
    avatar_url TEXT,
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    reset_token_expires TIMESTAMP,
    role VARCHAR(20) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- Create user_profiles table
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255),
    avatar_url TEXT,
    bio TEXT,
    website TEXT,
    wallet_address VARCHAR(255) UNIQUE,
    wallet_connected_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create categories table
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create agents table
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    category_id UUID REFERENCES categories(id),
    category VARCHAR(100) NOT NULL,
    creator_id UUID REFERENCES users(id),
    creator_username VARCHAR(50),
    image_url TEXT,
    price DECIMAL(10,6) DEFAULT 0.000001,
    rating DECIMAL(3,2) DEFAULT 0.00,
    deployments INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    technical_specs JSONB DEFAULT '{}',
    is_official BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create agent_reviews table
CREATE TABLE agent_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_id, user_id)
);

-- Create user_settings table
CREATE TABLE user_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    theme VARCHAR(20) DEFAULT 'dark',
    show_email BOOLEAN DEFAULT TRUE,
    bio TEXT,
    avatar_url TEXT,
    website TEXT,
    social_links JSONB DEFAULT '{}',
    notification_preferences JSONB DEFAULT '{"email": true, "push": false}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create agent_bookmarks table
CREATE TABLE agent_bookmarks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, agent_id)
);

-- Create user_agent_history table
CREATE TABLE user_agent_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    total_interactions INTEGER DEFAULT 0,
    total_tokens INTEGER DEFAULT 0,
    total_duration_ms BIGINT DEFAULT 0,
    first_interaction_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_interaction_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, agent_id)
);

-- Create chat_messages table
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
    content TEXT NOT NULL,
    is_bot BOOLEAN DEFAULT FALSE,
    is_markdown BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create discussions table
CREATE TABLE discussions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    is_markdown BOOLEAN DEFAULT FALSE,
    is_pinned BOOLEAN DEFAULT FALSE,
    is_locked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create discussion_replies table
CREATE TABLE discussion_replies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    discussion_id UUID REFERENCES discussions(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_markdown BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create discussion_likes table
CREATE TABLE discussion_likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    discussion_id UUID REFERENCES discussions(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(discussion_id, user_id)
);

-- Create reply_likes table
CREATE TABLE reply_likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    reply_id UUID REFERENCES discussion_replies(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(reply_id, user_id)
);

-- Create user_roles table for admin functionality
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL,
    granted_by UUID REFERENCES users(id),
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role)
);

-- Create user_bans table
CREATE TABLE user_bans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    banned_by UUID REFERENCES users(id),
    reason TEXT NOT NULL,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_activity table for tracking
CREATE TABLE user_activity (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    activity_type VARCHAR(50) NOT NULL,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create bot_analytics table
CREATE TABLE bot_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    total_interactions INTEGER DEFAULT 0,
    avg_response_time DECIMAL(10,2),
    accuracy_rating DECIMAL(3,2),
    daily_active_users INTEGER DEFAULT 0,
    date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_id, date)
);

-- Create content_reports table for moderation
CREATE TABLE content_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    reporter_id UUID REFERENCES users(id) ON DELETE CASCADE,
    content_type VARCHAR(20) NOT NULL, -- 'message', 'discussion', 'reply'
    content_id UUID NOT NULL,
    reason VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create moderation_actions table
CREATE TABLE moderation_actions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    moderator_id UUID REFERENCES users(id) ON DELETE CASCADE,
    report_id UUID REFERENCES content_reports(id) ON DELETE CASCADE,
    action VARCHAR(20) NOT NULL, -- 'dismiss', 'delete', 'ban_user'
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create usage_logs table for tracking API usage
CREATE TABLE usage_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    tool_id UUID,
    invoked_at TIMESTAMP NOT NULL,
    params JSONB DEFAULT '{}',
    duration_ms INTEGER,
    status VARCHAR(20),
    error_code VARCHAR(50),
    source VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create tool_reports table for detailed logging
CREATE TABLE tool_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tool_id UUID,
    user_id VARCHAR(255),
    input_payload JSONB,
    output_payload JSONB,
    duration_ms INTEGER,
    success BOOLEAN,
    error_msg TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_google_id ON users(google_id);
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_username ON user_profiles(username);
CREATE INDEX idx_user_profiles_wallet_address ON user_profiles(wallet_address);
CREATE INDEX idx_agents_category ON agents(category);
CREATE INDEX idx_agents_creator ON agents(creator_id);
CREATE INDEX idx_agents_slug ON agents(slug);
CREATE INDEX idx_agents_status ON agents(status);
CREATE INDEX idx_agent_reviews_agent_id ON agent_reviews(agent_id);
CREATE INDEX idx_agent_reviews_user_id ON agent_reviews(user_id);
CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX idx_agent_bookmarks_user_id ON agent_bookmarks(user_id);
CREATE INDEX idx_agent_bookmarks_agent_id ON agent_bookmarks(agent_id);
CREATE INDEX idx_user_agent_history_user_id ON user_agent_history(user_id);
CREATE INDEX idx_user_agent_history_agent_id ON user_agent_history(agent_id);
CREATE INDEX idx_chat_messages_user_id ON chat_messages(user_id);
CREATE INDEX idx_chat_messages_agent_id ON chat_messages(agent_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX idx_discussions_user_id ON discussions(user_id);
CREATE INDEX idx_discussion_replies_discussion_id ON discussion_replies(discussion_id);
CREATE INDEX idx_discussion_replies_user_id ON discussion_replies(user_id);
CREATE INDEX idx_discussion_likes_discussion_id ON discussion_likes(discussion_id);
CREATE INDEX idx_reply_likes_reply_id ON reply_likes(reply_id);
CREATE INDEX idx_user_activity_user_id ON user_activity(user_id);
CREATE INDEX idx_bot_analytics_agent_id ON bot_analytics(agent_id);
CREATE INDEX idx_content_reports_content_type_id ON content_reports(content_type, content_id);
CREATE INDEX idx_usage_logs_user_id ON usage_logs(user_id);
CREATE INDEX idx_usage_logs_created_at ON usage_logs(created_at);

-- Insert default categories
INSERT INTO categories (name, description, icon) VALUES
('AI Assistant', 'General purpose AI assistants', 'Bot'),
('Image Generation', 'AI tools for creating images', 'Image'),
('Code Assistant', 'Programming and development tools', 'Code'),
('Writing', 'Content creation and writing tools', 'FileText'),
('Analysis', 'Data analysis and research tools', 'BarChart'),
('Chat', 'Conversational AI agents', 'MessageSquare'),
('Tokenomics', 'Blockchain and tokenomics analysis', 'Coins'),
('Audit', 'Security and code audit tools', 'Shield');

-- Insert sample agents (will be populated by setup.sql)

-- Function to update agent rating when reviews change
CREATE OR REPLACE FUNCTION update_agent_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE agents 
    SET rating = (
        SELECT COALESCE(AVG(rating), 0)
        FROM agent_reviews 
        WHERE agent_id = COALESCE(NEW.agent_id, OLD.agent_id)
    )
    WHERE id = COALESCE(NEW.agent_id, OLD.agent_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update agent ratings
CREATE TRIGGER update_agent_rating_trigger
    AFTER INSERT OR UPDATE OR DELETE ON agent_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_agent_rating();

-- Function to get user dashboard stats
CREATE OR REPLACE FUNCTION get_user_dashboard_stats(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
    total_interactions_val INTEGER;
    total_duration_ms_val BIGINT;
    most_used_agent_val JSON;
    monthly_usage_val JSON;
BEGIN
    -- Get total interactions
    SELECT COALESCE(SUM(total_interactions), 0)
    INTO total_interactions_val
    FROM user_agent_history
    WHERE user_id = p_user_id;

    -- Get total usage duration in milliseconds
    SELECT COALESCE(SUM(total_duration_ms), 0)
    INTO total_duration_ms_val
    FROM user_agent_history
    WHERE user_id = p_user_id;

    -- Get most used agent
    SELECT json_build_object('name', a.name, 'count', uah.total_interactions)
    INTO most_used_agent_val
    FROM user_agent_history uah
    JOIN agents a ON uah.agent_id = a.id
    WHERE uah.user_id = p_user_id
    ORDER BY uah.total_interactions DESC
    LIMIT 1;

    -- Get monthly usage for the last 6 months
    SELECT json_agg(stats)
    INTO monthly_usage_val
    FROM (
        SELECT
            to_char(date_series.month, 'Mon') as month,
            COALESCE(SUM(uah.total_interactions), 0)::INTEGER as interactions
        FROM (
            SELECT date_trunc('month', generate_series(now() - interval '5 months', now(), '1 month')) as month
        ) as date_series
        LEFT JOIN user_agent_history uah
            ON date_trunc('month', uah.last_interaction_at) = date_series.month
            AND uah.user_id = p_user_id
        GROUP BY date_series.month
        ORDER BY date_series.month ASC
    ) as stats;

    -- Return all stats as a single JSON object
    RETURN json_build_object(
        'total_interactions', total_interactions_val,
        'total_duration_ms', total_duration_ms_val,
        'most_used_agent', most_used_agent_val,
        'monthly_usage', COALESCE(monthly_usage_val, '[]'::json)
    );
END;
$$ LANGUAGE plpgsql;

-- Function to update user agent history (for logging)
CREATE OR REPLACE FUNCTION update_user_agent_history(
    p_user_id UUID,
    p_agent_id UUID,
    p_duration_ms INTEGER
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO user_agent_history(user_id, agent_id, total_interactions, total_duration_ms, last_interaction_at)
    VALUES (p_user_id, p_agent_id, 1, p_duration_ms, NOW())
    ON CONFLICT (user_id, agent_id)
    DO UPDATE SET
        total_interactions = user_agent_history.total_interactions + 1,
        total_duration_ms = user_agent_history.total_duration_ms + p_duration_ms,
        last_interaction_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Admin helper functions
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users u 
        WHERE u.id = user_id AND u.role = 'admin'
    );
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION is_moderator(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users u 
        WHERE u.id = user_id AND u.role IN ('admin', 'moderator')
    );
END;
$$ LANGUAGE plpgsql; 