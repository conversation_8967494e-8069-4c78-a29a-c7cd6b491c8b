# Database Setup Guide

This guide will help you set up PostgreSQL for the Agora Marketplace application.

## Prerequisites

- PostgreSQL 12+ installed on your system
- Basic knowledge of terminal/command line

## Installing PostgreSQL

### macOS (using Homebrew)
```bash
brew install postgresql
brew services start postgresql
```

### Windows
1. Download PostgreSQL from https://www.postgresql.org/download/windows/
2. Run the installer and follow the setup wizard
3. Remember the password you set for the `postgres` user

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## Database Setup

### 1. Create Database and User

First, connect to PostgreSQL as the superuser:

```bash
# macOS/Linux
sudo -u postgres psql

# Windows (if postgres user is set up)
psql -U postgres
```

Then create the database and user:

```sql
-- Create the database
CREATE DATABASE agora_marketplace;

-- Create a user for the application
CREATE USER agora_user WITH PASSWORD 'agora_password';

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON DATABASE agora_marketplace TO agora_user;

-- Exit PostgreSQL
\q
```

### 2. Set Environment Variables

Create a `.env` file in the project root with:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=agora_marketplace
DB_USER=agora_user
DB_PASSWORD=agora_password

# Alternative: Use DATABASE_URL for production
# DATABASE_URL=postgresql://agora_user:agora_password@localhost:5432/agora_marketplace

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# API Keys (optional - add as needed)
GEMINI_API_KEY=your-gemini-api-key
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key
XAI_API_KEY=your-xai-api-key
STABILITY_API_KEY=your-stability-api-key

# Google OAuth (optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Session Secret
SESSION_SECRET=your-session-secret-change-this
```

### 3. Run Database Migrations

The application will automatically create tables on startup, but you can also run them manually:

```bash
# Create tables
npm run db:schema

# Or reset everything and recreate
npm run db:reset
```

### 4. Start the Application

```bash
npm start
```

## Database Schema

The database includes the following main tables:

- `users` - User authentication and basic info
- `user_profiles` - Extended user profiles
- `agents` - AI agents marketplace
- `categories` - Agent categories
- `chat_messages` - Chat history
- `discussions` - Community discussions
- `user_agent_history` - Usage tracking
- `agent_bookmarks` - User bookmarks
- `user_settings` - User preferences

## Common Issues

### Connection Refused
- Make sure PostgreSQL is running: `brew services status postgresql` (macOS)
- Check if the port 5432 is available: `lsof -i :5432`

### Authentication Failed
- Verify your database credentials in `.env`
- Make sure the user has the correct permissions

### Tables Not Created
- Check if the schema file exists at `database/schema.sql`
- Look at the server logs for any error messages

## Admin User

A default admin user is created during setup:
- Email: `<EMAIL>`
- Password: `admin123` (hashed)
- Role: `admin`

You can change this in the `database/setup.sql` file.

## Development Tips

1. **Reset Database**: Use `npm run db:reset` to start fresh
2. **View Data**: Use a PostgreSQL client like pgAdmin or DBeaver
3. **Backup**: `pg_dump agora_marketplace > backup.sql`
4. **Restore**: `psql agora_marketplace < backup.sql`

## Production Deployment

For production:

1. Use a managed PostgreSQL service (AWS RDS, Google Cloud SQL, etc.)
2. Set `DATABASE_URL` environment variable
3. Enable SSL: `DATABASE_URL=********************************/db?sslmode=require`
4. Use strong passwords and secrets
5. Enable connection pooling if needed

## Troubleshooting

### Check PostgreSQL Status
```bash
# macOS
brew services list | grep postgresql

# Linux
sudo systemctl status postgresql

# Windows
services.msc (look for postgresql service)
```

### Check Database Connection
```bash
psql -h localhost -U agora_user -d agora_marketplace
```

### View Logs
```bash
# macOS (Homebrew)
tail -f /usr/local/var/log/<EMAIL>

# Linux
sudo tail -f /var/log/postgresql/postgresql-14-main.log
```

Need help? Check the application logs or create an issue in the repository. 