# Agora: AI Agent Marketplace

![agora-picture-bg](https://github.com/user-attachments/assets/225ea16b-22b5-4aef-9eb3-8775f426b3bc)

DanteGPU is an open marketplace for AI agents, chatbots, and applications, powered by the Solana blockchain. Our mission is to democratize access to AI by allowing anyone to publish their AI-enhanced tools and enabling users to access them with our native utility token, $dGPU.

---

## 📙 Table of Contents

- [1. Overview](#1-overview)
- [2. Key Features](#2-key-features)
- [3. <PERSON>ack](#3-tech-stack)
- [4. Architecture](#4-architecture)
- [5. Local Development](#5-local-development)
- [6. Deployment](#6-deployment)
- [7. Contributing](#7-contributing)
- [8. License](#8-license)

---

## 1. Overview

Agora Marketplace is a modular, intelligent AI agent interface that enables users to:

- Discover and interact with a diverse range of AI agents.
- Pay for usage with the native $dGPU token on the Solana blockchain.
- Experience real-time, streaming responses with support for markdown and images.
- Create and publish their own agents to the marketplace.

---

## 2. Key Features

- **Dynamic Agent Discovery**: Explore agents by category, ranking, or popularity.
- **Performant Frontend**: Lightning-fast UI built with React, Vite, and Tailwind CSS, optimized with code-splitting.
- **Robust Backend**: A professional Node.js Express server with a clear `controller-route` architecture.
- **Secure and Scalable**: Features rate limiting, structured validation, and centralized error handling.
- **Decentralized Auth**: Seamless user authentication via Supabase and Solana wallets.
- **Usage & Analytics**: Detailed logging of agent usage and performance.
- **Containerized**: Ready for production deployment with a multi-stage `Dockerfile`.

---

## 3. Tech Stack

| Layer          | Technology                                         | Description                               |
|----------------|----------------------------------------------------|-------------------------------------------|
| **Frontend**   | React, Vite, Tailwind CSS                          | Modern, fast, and responsive user interface. |
| **State Mgmt** | Zustand                                            | Simple and scalable state management.      |
| **Backend**    | Node.js, Express                                   | Scalable server for proxying API requests. |
| **Validation** | Zod                                                | Robust schema validation for API inputs.  |
| **Security**   | `express-rate-limit`                               | Protection against brute-force attacks.   |
| **Database**   | Supabase (PostgreSQL)                              | User profiles, agent data, and usage logs.|
| **Auth**       | Supabase Auth, Solana Wallet Adapter               | Secure email/pass, social, and wallet login.|
| **Deployment** | Docker                                             | Consistent, containerized environments.   |

---

## 4. Architecture

The backend is built using a professional and maintainable structure that separates concerns into distinct modules.

```txt
/server.js              # Main Express server setup and middleware registration
/api
├── /controllers        # Contains the business logic for each route (e.g., llmController.js)
├── /routes             # Defines the API endpoints and connects them to controllers (e.g., llmRoutes.js)
├── /middleware         # Custom middleware for logging, auth, and validation (e.g., validate.js)
└── /validators         # Zod schemas for validating request bodies and params (e.g., llmValidators.js)
/src                    # React frontend source code
/Dockerfile             # Instructions to build the production-ready backend container
.env                    # Environment variables (ALWAYS keep this private)
```

This architecture ensures that the code is easy to navigate, test, and scale.

---

## 5. Local Development

### 5.1 Requirements

- Node.js `v16.x` or higher
- PostgreSQL `v14` or higher
- API keys for AI services (Gemini, OpenAI, Anthropic, etc.)
- Solana wallet for dGPU token integration (optional)

### 5.2 Setup

1.  **Clone the repository:**
```bash
    git clone https://github.com/dante-gpu/agora-marketplace.git
    cd agora-marketplace
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Install and start PostgreSQL:**
    ```bash
    # macOS with Homebrew
    brew install postgresql@14
    brew services start postgresql@14

    # Ubuntu/Debian
    sudo apt-get install postgresql postgresql-contrib
    sudo systemctl start postgresql
    ```

4.  **Set up the database:**
    ```bash
    # Run the automated setup script
    chmod +x setup-database.sh
    ./setup-database.sh

    # Or manually:
    createdb agora_marketplace
    psql -d agora_marketplace -f database/schema.sql
    psql -d agora_marketplace -f database/setup.sql
    ```

5.  **Set up environment variables:**
    Create a `.env` file in the root directory and populate it with your keys:
    ```bash
    cp .env.example .env
    # Edit .env with your actual API keys and configuration
    ```

4.  **Start the services:**
    - **Frontend (Vite Dev Server):** This command starts the React app on `http://localhost:5174`.
      ```bash
      npm run dev
      ```
    - **Backend (Node.js Express Server):** This command starts the backend API proxy on `http://localhost:8787`.
```bash
      node server.js
```

---

## 6. Deployment

This project is configured for a professional, production-ready deployment using containers.

### 6.1 Backend (Express Server)

The backend is designed to be deployed as a Docker container. The included `Dockerfile` creates an optimized, multi-stage build.

**Recommended Hosting Services:**
- **Render**: Excellent support for deploying Docker containers from a Git repository with automatic builds.
- **Fly.io**: Allows you to deploy applications close to your users for lower latency.
- **AWS / Google Cloud**: For more advanced configurations and scalability needs.

**General Steps to Deploy:**

1.  Push your code to a GitHub/GitLab repository.
2.  Connect your repository to a service like Render.
3.  Create a new "Web Service" and point it to your repository.
4.  The service should detect the `Dockerfile` and build/deploy your container automatically.
5.  **Important:** Remember to configure all your environment variables (API keys, Supabase URL, etc.) in the hosting service's dashboard.

### 6.2 Frontend (React/Vite)

The frontend is a static site and can be deployed easily on modern hosting platforms.

**Recommended Hosting Services:**
- **Vercel**: Offers a seamless, zero-configuration deployment experience for Vite/React apps.
- **Netlify**: Another excellent choice with features like continuous deployment and serverless functions.
- **GitHub Pages**: A free option for simple static sites.

**General Steps to Deploy:**

1.  Push your code to a GitHub/GitLab repository.
2.  Connect your repository to Vercel or Netlify.
3.  The platform will automatically detect it's a Vite project, build it, and deploy it to a global CDN.
4.  Ensure your `VITE_SUPABASE_URL`, `VITE_SUPABASE_ANON_KEY`, and the URL of your deployed backend are set as environment variables in the platform's dashboard.

---

## 7. Contributing

Contributions are welcome! Please open an issue or submit a pull request for any improvements.

```bash
git checkout -b feature/my-awesome-change
# Make your edits
git commit -m "feat: Describe your awesome contribution"
git push origin feature/my-awesome-change
```

Please ensure your code is linted before submitting:
```bash
npm run lint
```

---

## 8. License

Agora Marketplace is licensed under the MIT License.

---

<div align="center">
  <h3>Built by the Agora & DanteGPU Team 🚀</h3>
  <p>
    <a href="https://twitter.com/dantegpu">Twitter</a> •
    <a href="https://agora.market">Website</a> •
    <a href="https://github.com/dante-gpu/agora-marketplace">GitHub</a>
  </p>
</div>
