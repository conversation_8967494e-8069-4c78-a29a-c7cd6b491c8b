<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agora Marketplace - Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #0f0f0f;
            color: #ffffff;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .agent-card {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            transition: transform 0.2s;
        }
        .agent-card:hover {
            transform: translateY(-2px);
            border-color: #555;
        }
        .agent-image {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 15px;
        }
        .agent-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #00d4ff;
        }
        .agent-description {
            color: #ccc;
            margin-bottom: 15px;
            line-height: 1.4;
        }
        .agent-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #888;
        }
        .category-tag {
            background: #333;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .loading {
            text-align: center;
            color: #888;
        }
        .error {
            color: #ff4444;
            text-align: center;
            padding: 20px;
            background: #2a1a1a;
            border-radius: 8px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .stat-card {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #00d4ff;
        }
        .stat-label {
            color: #888;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Agora AI Agent Marketplace</h1>
        <p>Discover and interact with powerful AI agents</p>
    </div>

    <div class="stats" id="stats">
        <div class="stat-card">
            <div class="stat-number" id="total-agents">-</div>
            <div class="stat-label">Total Agents</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="total-categories">-</div>
            <div class="stat-label">Categories</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" id="server-status">-</div>
            <div class="stat-label">Server Status</div>
        </div>
    </div>

    <div id="agents-container">
        <div class="loading">Loading agents...</div>
    </div>

    <script>
        async function fetchData() {
            try {
                // Test server health
                const healthResponse = await fetch('/api/health');
                const healthData = await healthResponse.json();
                document.getElementById('server-status').textContent = healthData.status;
                document.getElementById('server-status').style.color = healthData.status === 'healthy' ? '#00ff88' : '#ff4444';

                // Fetch agents
                const agentsResponse = await fetch('/api/agents/agents');
                const agentsData = await agentsResponse.json();
                
                if (agentsData.success) {
                    const agents = agentsData.data.agents;
                    document.getElementById('total-agents').textContent = agents.length;
                    
                    // Fetch categories
                    const categoriesResponse = await fetch('/api/agents/categories');
                    const categoriesData = await categoriesResponse.json();
                    
                    if (categoriesData.success) {
                        document.getElementById('total-categories').textContent = categoriesData.data.categories.length;
                    }
                    
                    // Render agents
                    renderAgents(agents);
                } else {
                    throw new Error(agentsData.error?.message || 'Failed to fetch agents');
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('agents-container').innerHTML = `
                    <div class="error">
                        <h3>Error loading data</h3>
                        <p>${error.message}</p>
                        <p>Make sure the server is running on port 8787</p>
                    </div>
                `;
            }
        }

        function renderAgents(agents) {
            const container = document.getElementById('agents-container');
            
            if (agents.length === 0) {
                container.innerHTML = '<div class="loading">No agents found</div>';
                return;
            }

            const agentsHTML = agents.map(agent => `
                <div class="agent-card">
                    <img src="${agent.image_url}" alt="${agent.name}" class="agent-image" />
                    <div class="agent-name">${agent.name}</div>
                    <div class="agent-description">${agent.description}</div>
                    <div class="agent-meta">
                        <span class="category-tag">${agent.category}</span>
                        <span>⭐ ${agent.rating} • 🚀 ${agent.deployments}</span>
                    </div>
                </div>
            `).join('');

            container.innerHTML = `<div class="agents-grid">${agentsHTML}</div>`;
        }

        // Load data when page loads
        fetchData();
    </script>
</body>
</html>
