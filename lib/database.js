const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration for local PostgreSQL
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'agora_marketplace',
  user: process.env.DB_USER || 'agora_user',
  password: process.env.DB_PASSWORD || 'agora_password',
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

// If DATABASE_URL is provided (for production), use it instead
if (process.env.DATABASE_URL) {
  dbConfig.connectionString = process.env.DATABASE_URL;
  if (process.env.NODE_ENV === 'production') {
    dbConfig.ssl = { rejectUnauthorized: false };
  }
}

const pool = new Pool(dbConfig);

// Test database connection
const testConnection = async () => {
  try {
    const client = await pool.connect();
    console.log('[Database] Connected successfully to PostgreSQL');
    
    // Test basic query
    const result = await client.query('SELECT NOW()');
    console.log('[Database] Connection test successful:', result.rows[0].now);
    
    client.release();
    return true;
  } catch (error) {
    console.error('[Database] Connection failed:', error.message);
    return false;
  }
};

// Initialize database with schema
const initializeDatabase = async () => {
  try {
    console.log('[Database] Initializing database...');
    
    // Check if schema.sql exists
    const schemaPath = path.join(__dirname, '../database/schema.sql');
    if (!fs.existsSync(schemaPath)) {
      console.error('[Database] Schema file not found at:', schemaPath);
      console.log('[Database] Please ensure database/schema.sql exists');
      return false;
    }

    // Run schema if needed
    const client = await pool.connect();
    try {
      // Check if tables exist
      const result = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'users'
      `);
      
      if (result.rows.length === 0) {
        console.log('[Database] No tables found, running schema...');
        
        // Read and execute schema
        const schema = fs.readFileSync(schemaPath, 'utf8');
        await client.query(schema);
        
        console.log('[Database] Schema executed successfully');
      } else {
        console.log('[Database] Tables already exist, skipping schema execution');
      }
      
    } finally {
      client.release();
    }
    
    console.log('[Database] Database initialization completed');
    return true;
    
  } catch (error) {
    console.error('[Database] Initialization failed:', error);
    console.error('[Database] Make sure PostgreSQL is running and accessible');
    return false;
  }
};

// Graceful shutdown
const shutdown = async () => {
  try {
    await pool.end();
    console.log('[Database] Connection pool closed');
  } catch (error) {
    console.error('[Database] Error during shutdown:', error);
  }
};

// Error handling for the pool
pool.on('error', (err) => {
  console.error('[Database] Pool error:', err);
});

// Initialize database on startup
initializeDatabase().catch(console.error);

module.exports = {
  pool,
  testConnection,
  initializeDatabase,
  shutdown
};