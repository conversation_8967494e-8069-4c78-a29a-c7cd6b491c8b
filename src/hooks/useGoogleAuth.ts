import { useEffect, useState } from 'react';
import { useAuthStore } from '../store/authStore';

declare global {
  interface Window {
    google: any;
    gapi: any;
  }
}

export const useGoogleAuth = () => {
  const [isGoogleLoaded, setIsGoogleLoaded] = useState(false);
  const { signInWithGoogle, loading } = useAuthStore();

  useEffect(() => {
    const loadGoogleScript = () => {
      if (window.google) {
        setIsGoogleLoaded(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = () => {
        if (window.google) {
          window.google.accounts.id.initialize({
            client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
            callback: handleCredentialResponse,
            auto_select: false,
            cancel_on_tap_outside: true,
          });
          setIsGoogleLoaded(true);
        }
      };
      script.onerror = () => {
        console.error('Failed to load Google Identity Services');
      };
      document.head.appendChild(script);
    };

    const handleCredentialResponse = (response: any) => {
      if (response.credential) {
        signInWithGoogle(response.credential);
      }
    };

    loadGoogleScript();

    return () => {
      // Cleanup if needed
      const script = document.querySelector('script[src="https://accounts.google.com/gsi/client"]');
      if (script) {
        script.remove();
      }
    };
  }, [signInWithGoogle]);

  const signInWithGooglePopup = () => {
    if (!isGoogleLoaded || !window.google) {
      console.error('Google Identity Services not loaded');
      return;
    }

    try {
      window.google.accounts.id.prompt((notification: any) => {
        if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
          // If One Tap is not displayed or skipped, show the button popup
          window.google.accounts.id.renderButton(
            document.getElementById('g_id_signin'),
            {
              theme: 'outline',
              size: 'large',
              type: 'standard',
              text: 'signin_with',
              logo_alignment: 'left',
            }
          );
        }
      });
    } catch (error) {
      console.error('Google sign-in failed:', error);
    }
  };

  const renderGoogleButton = (elementId: string) => {
    if (!isGoogleLoaded || !window.google) {
      return false;
    }

    try {
      window.google.accounts.id.renderButton(
        document.getElementById(elementId),
        {
          theme: 'outline',
          size: 'large',
          type: 'standard',
          text: 'signin_with',
          logo_alignment: 'left',
          width: '100%',
        }
      );
      return true;
    } catch (error) {
      console.error('Failed to render Google button:', error);
      return false;
    }
  };

  return {
    isGoogleLoaded,
    signInWithGooglePopup,
    renderGoogleButton,
    loading,
  };
}; 