@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    font-family: 'Space Grotesk', sans-serif;
    @apply bg-black text-white;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Plus Jakarta Sans', sans-serif;
  }
}

@layer components {
  .highlight {
    @apply bg-[#e1ffa6] text-black px-2 py-1 rounded-lg;
  }

  .btn-primary {
    @apply px-6 py-2.5 bg-gradient-to-r from-[#e1ffa6] to-[#9dff00] text-black font-medium rounded-lg 
           hover:opacity-90 transition-all duration-200 shadow-lg shadow-[#e1ffa6]/20
           active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply px-6 py-2.5 border border-[#e1ffa6] text-[#e1ffa6] font-medium rounded-lg 
           hover:bg-[#e1ffa6]/10 transition-all duration-200
           active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .card-hover {
    @apply hover:border-[#e1ffa6] hover:shadow-lg hover:shadow-[#e1ffa6]/10 transition-all duration-200;
  }

  .glass-effect {
    @apply bg-white/5 backdrop-blur-lg border border-white/10;
  }

  .input-field {
    @apply w-full bg-black/50 border border-gray-800 rounded-lg p-3 
           focus:outline-none focus:border-[#e1ffa6] focus:ring-1 focus:ring-[#e1ffa6]
           transition-all duration-200;
  }

  /* Wallet Adapter Custom Styles */
  .wallet-adapter-button {
    @apply !flex !items-center !justify-center !gap-2 !bg-gradient-to-r !from-[#e1ffa6] !to-[#9dff00] !text-black 
           !border-none !rounded-full !h-auto !py-3 !px-8 !font-medium !text-sm
           hover:!opacity-90 !transition-all !duration-200 !shadow-lg !shadow-[#e1ffa6]/20;
  }

  .wallet-adapter-modal-wrapper {
    @apply !bg-black !border !border-[#e1ffa6] !rounded-2xl !shadow-xl !shadow-[#e1ffa6]/10;
  }

  .wallet-adapter-modal-button-close {
    @apply !bg-[#e1ffa6]/10 !text-[#e1ffa6] hover:!bg-[#e1ffa6]/20 !transition-colors !rounded-full;
  }

  .wallet-adapter-modal-title {
    @apply !text-white !font-bold !text-2xl;
  }

  .wallet-adapter-modal-content {
    @apply !text-gray-400;
  }

  .wallet-adapter-modal-list {
    @apply !space-y-2;
  }

  .wallet-adapter-modal-list-more {
    @apply !text-[#e1ffa6] hover:!text-[#e1ffa6]/80 !transition-colors;
  }

  .wallet-adapter-dropdown {
    @apply !font-medium !w-full;
  }

  .wallet-adapter-button[disabled] {
    @apply !opacity-50 !cursor-not-allowed;
  }

  .wallet-adapter-button-trigger {
    @apply !bg-gradient-to-r !from-[#e1ffa6] !to-[#9dff00] !text-black !w-full;
  }

  .wallet-adapter-modal-list-item {
    @apply !bg-[#e1ffa6]/5 !rounded-xl hover:!bg-[#e1ffa6]/10 !transition-colors !border !border-[#e1ffa6]/20;
  }

  .wallet-adapter-modal-list-item-icon {
    @apply !rounded-xl;
  }

  .wallet-adapter-modal-list-item-name {
    @apply !font-medium;
  }
}