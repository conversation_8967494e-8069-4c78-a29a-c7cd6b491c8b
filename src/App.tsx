import React, { useEffect, Suspense, lazy } from 'react';
import { Routes, Route } from 'react-router-dom';
import { SolanaWalletProvider } from './providers/WalletProvider';
import Navbar from './components/Navbar';
import QuickChat from './components/QuickChat';
import TokenPrice from './components/TokenPrice';
import Footer from './components/Footer';
import LoadingSpinner from './components/LoadingSpinner'; // Generic loading component
import { Toaster } from 'react-hot-toast';

// --- Lazy Load Pages ---
const Home = lazy(() => import('./pages/Home'));
const Explore = lazy(() => import('./pages/Explore'));
const Categories = lazy(() => import('./pages/Categories'));
const Rankings = lazy(() => import('./pages/Rankings'));
const Create = lazy(() => import('./pages/Create'));
const AgentDetails = lazy(() => import('./pages/AgentDetails'));
const Profile = lazy(() => import('./pages/Profile'));
const SignIn = lazy(() => import('./pages/SignIn'));
const SignUp = lazy(() => import('./pages/SignUp'));
const Discussions = lazy(() => import('./pages/Discussions'));
const Chat = lazy(() => import('./pages/Chat'));
const Dashboard = lazy(() => import('./pages/Dashboard'));

// --- Lazy Load Admin Pages ---
const AdminDashboard = lazy(() => import('./pages/admin/Dashboard'));
const Users = lazy(() => import('./pages/admin/Users'));
const Bots = lazy(() => import('./pages/admin/Bots'));
const Moderation = lazy(() => import('./pages/admin/Moderation'));
const Analytics = lazy(() => import('./pages/admin/Analytics'));

function App() {
  const isChatWindow = window.location.pathname === '/chat';

  if (isChatWindow) {
    return (
      <Suspense fallback={<div className="flex h-screen w-full items-center justify-center bg-black"><LoadingSpinner size="lg" /></div>}>
        <Chat />
      </Suspense>
    );
  }

  return (
    <SolanaWalletProvider>
      <Toaster
        position="top-right"
        toastOptions={{
          className: '',
          style: {
            background: '#333',
            color: '#fff',
            border: '1px solid #555',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: '#10B981',
              secondary: 'white',
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: '#F87171',
              secondary: 'white',
            },
          },
        }}
      />
      <div className="flex flex-col min-h-screen bg-black">
        <Navbar />

        <main className="flex-grow">
          <Suspense fallback={<div className="flex h-64 w-full items-center justify-center"><LoadingSpinner size="lg" /></div>}>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/explore" element={<Explore />} />
              <Route path="/categories" element={<Categories />} />
              <Route path="/rankings" element={<Rankings />} />
              <Route path="/create" element={<Create />} />
              <Route path="/agent/:slug" element={<AgentDetails />} />
              <Route path="/profile" element={<Profile />} />
              <Route path="/profile/:username" element={<Profile />} />
              <Route path="/signin" element={<SignIn />} />
              <Route path="/signup" element={<SignUp />} />
              <Route path="/discussions" element={<Discussions />} />
              <Route path="/chat" element={<Chat />} />
              <Route path="/dashboard" element={<Dashboard />} />

              <Route path="/admin" element={<AdminDashboard />}>
                <Route path="users" element={<Users />} />
                <Route path="bots" element={<Bots />} />
                <Route path="moderation" element={<Moderation />} />
                <Route path="analytics" element={<Analytics />} />
              </Route>
            </Routes>
          </Suspense>
        </main>

        <QuickChat />
        <TokenPrice />

        <Footer />
      </div>
    </SolanaWalletProvider>
  );
}

export default App;
