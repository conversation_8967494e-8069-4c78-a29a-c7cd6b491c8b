import { create } from "zustand";
import { PublicKey } from "@solana/web3.js";
import toast from 'react-hot-toast';
import axios from 'axios';

// Configure axios defaults
axios.defaults.baseURL = 'http://localhost:8787';

interface User {
  id: string;
  email: string;
  username: string;
  avatar_url: string;
  bio?: string;
  website?: string;
  wallet_address?: string;
  email_verified: boolean;
  created_at: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
  signUp: (email: string, password: string, username: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signInWithGoogle: (idToken: string) => Promise<void>;
  signInWithWallet: (signMessage: (message: Uint8Array) => Promise<Uint8Array>, publicKey: PublicKey) => Promise<void>;
  signOut: () => void;
  getCurrentUser: () => Promise<void>;
  initialize: () => void;
}

// Token management
const getStoredToken = (): string | null => {
  return localStorage.getItem('agora_token');
};

const setStoredToken = (token: string): void => {
  localStorage.setItem('agora_token', token);
  // Set default authorization header for axios
  axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
};

const removeStoredToken = (): void => {
  localStorage.removeItem('agora_token');
  delete axios.defaults.headers.common['Authorization'];
};

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  token: null,
  loading: false,
  error: null,

  signUp: async (email, password, username) => {
    set({ loading: true, error: null });
    
    try {
      const response = await axios.post('/api/auth/signup', {
        email,
        password,
        username
      });

      const { user, token } = response.data;
      
      setStoredToken(token);
      set({ 
        user, 
        token, 
        loading: false,
        error: null 
      });
      
      toast.success('Account created successfully!');
      
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Registration failed';
      set({ 
        error: errorMessage, 
        loading: false 
      });
      toast.error(errorMessage);
    }
  },

  signIn: async (email, password) => {
    set({ loading: true, error: null });
    
    try {
      const response = await axios.post('/api/auth/signin', {
        email,
        password
      });

      const { user, token } = response.data;
      
      setStoredToken(token);
      set({ 
        user, 
        token, 
        loading: false,
        error: null 
      });
      
      toast.success('Welcome back!');
      
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Sign in failed';
      set({ 
        error: errorMessage, 
        loading: false 
      });
      toast.error(errorMessage);
    }
  },

  signInWithGoogle: async (idToken: string) => {
    set({ loading: true, error: null });
    
    try {
      const response = await axios.post('/api/auth/google', {
        idToken
      });

      const { user, token } = response.data;
      
      setStoredToken(token);
      set({ 
        user, 
        token, 
        loading: false,
        error: null 
      });
      
      toast.success('Google sign-in successful!');
      
    } catch (error: any) {
      console.error("[Auth] Google sign-in failed:", error);
      const errorMessage = error.response?.data?.error?.message || error.message || 'Google sign-in failed';
      set({ 
        error: errorMessage, 
        loading: false 
      });
      toast.error(`Google sign-in failed: ${errorMessage}`);
    }
  },

  signInWithWallet: async (signMessage, publicKey) => {
    set({ loading: true, error: null });
    
    try {
      // Get nonce from server
      const nonceResponse = await axios.post('/api/auth/nonce');
      const { nonce } = nonceResponse.data;

      if (!nonce) {
        throw new Error('Failed to retrieve security nonce from server.');
      }

      // Sign the message
      const message = `Please sign this message to log in to Agora Marketplace. Nonce: ${nonce}`;
      const messageBytes = new TextEncoder().encode(message);
      const signatureBytes = await signMessage(messageBytes);
      const signature = Array.from(signatureBytes);

      // Verify with server
      const response = await axios.post('/api/auth/verify', {
        publicKey: publicKey.toBase58(),
        signature,
        nonce
      });

      const { user, token } = response.data;
      
      setStoredToken(token);
      set({ 
        user, 
        token, 
        loading: false,
        error: null 
      });
      
      toast.success('Wallet sign-in successful!');
      
    } catch (error: any) {
      console.error("[Auth] Wallet sign-in failed:", error);
      const errorMessage = error.response?.data?.error?.message || error.message || 'Wallet sign-in failed';
      set({ 
        error: errorMessage, 
        loading: false 
      });
      toast.error(`Wallet sign-in failed: ${errorMessage}`);
    }
  },

  signOut: () => {
    removeStoredToken();
    set({ 
      user: null, 
      token: null, 
      error: null 
    });
    toast.success('Signed out successfully');
  },

  getCurrentUser: async () => {
    const token = getStoredToken();
    
    if (!token) {
      set({ loading: false });
      return;
    }

    set({ loading: true });
    
    try {
      // Set token in headers
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      const response = await axios.get('/api/auth/user');
      const { user } = response.data;
      
      set({ 
        user, 
        token, 
        loading: false,
        error: null 
      });
      
    } catch (error: any) {
      console.error('[Auth] Get current user failed:', error);
      // Token is invalid, remove it
      removeStoredToken();
      set({ 
        user: null, 
        token: null, 
        loading: false,
        error: null 
      });
    }
  },

  initialize: () => {
    const token = getStoredToken();
    
    if (token) {
      // Set token in axios headers
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      set({ token });
      // Get current user
      get().getCurrentUser();
    } else {
      set({ loading: false });
    }
  },
}));

// Initialize auth on app start
if (typeof window !== 'undefined') {
  useAuthStore.getState().initialize();
}
