import { create } from 'zustand';
import { Connection, PublicKey } from '@solana/web3.js';
import { getDGpuBalance } from '../utils/getDGpuBalance';
import { useAuthStore } from './authStore';
import axios from 'axios';

interface WalletState {
  connected: boolean;
  address: string | null;
  dGpuBalance: number;
  loading: boolean;
  error: string | null;
  fetchDGpuBalance: (connection: Connection, publicKey: PublicKey) => Promise<void>;
  connectWallet: (publicKey: PublicKey, connection: Connection) => Promise<void>;
  disconnectWallet: () => Promise<void>;
}

export const useWalletStore = create<WalletState>((set, get) => ({
  connected: false,
  address: null,
  dGpuBalance: 0,
  loading: false,
  error: null,

  fetchDGpuBalance: async (connection: Connection, publicKey: PublicKey) => {
    set({ loading: true });
    try {
      const balance = await getDGpuBalance(connection, publicKey);
      set({ dGpuBalance: balance, loading: false });
    } catch (err) {
      console.error("Failed to fetch dGPU balance:", err);
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      set({ error: errorMessage, loading: false, dGpuBalance: 0 });
    }
  },

  connectWallet: async (publicKey: PublicKey, connection: Connection) => {
    const address = publicKey.toBase58();
    set({ connected: true, address, loading: true, error: null });
    
    // Link wallet to user profile via API
    const { user } = useAuthStore.getState();
    if (user) {
      try {
        await axios.put('/api/profile/profile', { 
          wallet_address: address 
        });
      } catch (error) {
        console.error("Error linking wallet to profile:", error);
        set({ error: "Failed to link wallet to your profile." });
      }
    }
    
    // Now that the wallet is connected, fetch the balance.
    await get().fetchDGpuBalance(connection, publicKey);
    
    set({ loading: false });
  },

  disconnectWallet: async () => {
    set({ connected: false, address: null, dGpuBalance: 0, error: null });
  },
}));