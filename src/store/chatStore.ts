import { create } from 'zustand';
import axios from 'axios';

export interface Message {
  id: string;
  content: string;
  is_bot: boolean;
  is_markdown: boolean;
  created_at: string;
  agent_name?: string;
}

interface ChatState {
  messages: Message[];
  loading: boolean;
  error: string | null;
  sendMessage: (
    content: string,
    isMarkdown?: boolean,
    agentId?: string
  ) => Promise<void>;
  saveBotResponse: (
    content: string,
    isMarkdown?: boolean,
    agentId?: string
  ) => Promise<void>;
  fetchMessages: (agentId?: string) => Promise<void>;
  clearMessages: (agentId?: string) => Promise<void>;
  updateInteractionHistory: (agentId: string, durationMs?: number) => Promise<void>;
}

export const useChatStore = create<ChatState>((set, get) => ({
  messages: [],
  loading: false,
  error: null,

  sendMessage: async (content, isMarkdown = false, agentId) => {
    try {
      set({ loading: true, error: null });
      
      const response = await axios.post('/api/chat/messages', {
        content,
        is_markdown: isMarkdown,
        agent_id: agentId
      });

      set(state => ({
        messages: [...state.messages, response.data.message],
        loading: false
      }));
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
      throw error;
    }
  },

  saveBotResponse: async (content, isMarkdown = true, agentId) => {
    try {
      set({ loading: true, error: null });
      
      const response = await axios.post('/api/chat/messages/bot-response', {
        content,
        is_markdown: isMarkdown,
        agent_id: agentId
      });

      set(state => ({
        messages: [...state.messages, response.data.message],
        loading: false
      }));
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
      throw error;
    }
  },

  fetchMessages: async (agentId) => {
    try {
      set({ loading: true, error: null });
      
      const params = agentId ? `?agentId=${agentId}` : '';
      const response = await axios.get(`/api/chat/messages${params}`);
      
      set({ messages: response.data.messages, loading: false });
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
    }
  },

  clearMessages: async (agentId) => {
    try {
      set({ loading: true, error: null });
      
      const params = agentId ? `?agentId=${agentId}` : '';
      await axios.delete(`/api/chat/messages${params}`);
      
      set({ messages: [], loading: false });
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
      throw error;
    }
  },

  updateInteractionHistory: async (agentId, durationMs = 0) => {
    try {
      await axios.post('/api/chat/interaction-history', {
        agent_id: agentId,
        duration_ms: durationMs
      });
    } catch (error: any) {
      console.error('Failed to update interaction history:', error);
      // Don't throw error for history updates
    }
  },
}));
