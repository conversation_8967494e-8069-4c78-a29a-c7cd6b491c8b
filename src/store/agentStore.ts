import { create } from 'zustand';
import { agentsAPI } from '../lib/api';

export interface Agent {
  id: string;
  name: string;
  description: string;
  price: number;
  creator: string;
  creator_username?: string;
  image_url: string;
  category: string;
  rating: number;
  deployments: number;
  created_at: string;
  slug: string;
  technical_specs: Record<string, any>;
  status: string;
  updated_at: string;
  is_official?: boolean;
}

interface AgentState {
  agents: Agent[];
  featuredAgents: Agent[];
  categories: any[];
  rankings: Agent[];
  loading: boolean;
  error: string | null;
  
  fetchAgents: (filters?: any) => Promise<void>;
  fetchFeaturedAgents: () => Promise<void>;
  fetchCategories: () => Promise<void>;
  fetchRankings: (sortBy?: string) => Promise<void>;
  createAgent: (agent: Omit<Agent, 'id' | 'rating' | 'deployments' | 'created_at' | 'updated_at' | 'slug'>) => Promise<void>;
  updateAgent: (id: string, updates: Partial<Agent>) => Promise<void>;
  getAgentBySlug: (slug: string) => Promise<Agent | null>;
}

export const useAgentStore = create<AgentState>((set, get) => ({
  agents: [],
  featuredAgents: [],
  categories: [],
  rankings: [],
  loading: false,
  error: null,

  fetchAgents: async (filters = {}) => {
    try {
      set({ loading: true, error: null });

      const response = await agentsAPI.getAgents(filters);
      set({ agents: response.data.agents, loading: false });
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
    }
  },

  fetchFeaturedAgents: async () => {
    try {
      set({ loading: true, error: null });

      const response = await agentsAPI.getFeaturedAgents();
      set({ featuredAgents: response.data.agents, loading: false });
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
    }
  },

  fetchCategories: async () => {
    try {
      set({ loading: true, error: null });

      const response = await agentsAPI.getCategories();
      set({ categories: response.data.categories, loading: false });
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
    }
  },

  fetchRankings: async (sortBy = 'rating') => {
    try {
      set({ loading: true, error: null });

      const response = await agentsAPI.getRankings(sortBy);
      set({ rankings: response.data.rankings, loading: false });
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
    }
  },

  createAgent: async (agentData) => {
    try {
      set({ loading: true, error: null });

      const response = await agentsAPI.createAgent(agentData);
      set(state => ({
        agents: [response.data.agent, ...state.agents],
        loading: false
      }));
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
      throw error;
    }
  },

  updateAgent: async (id, updates) => {
    try {
      set({ loading: true, error: null });

      const response = await agentsAPI.updateAgent(id, updates);
      set(state => ({
        agents: state.agents.map(agent =>
          agent.id === id ? response.data.agent : agent
        ),
        loading: false
      }));
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
      throw error;
    }
  },

  getAgentBySlug: async (slug) => {
    try {
      set({ loading: true, error: null });

      const response = await agentsAPI.getAgentBySlug(slug);
      set({ loading: false });
      return response.data.agent;
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
      return null;
    }
  },
}));