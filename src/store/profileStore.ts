import { create } from 'zustand';
import axios from 'axios';

export interface UserSettings {
  id: string;
  theme: string;
  show_email: boolean;
  bio: string | null;
  avatar_url: string | null;
  website: string | null;
  social_links: Record<string, string>;
  notification_preferences: {
    email: boolean;
    push: boolean;
  };
  wallet_address?: string | null;
  wallet_connected_at?: string | null;
}

export interface AgentBookmark {
  id: string;
  agent_id: string;
  notes: string | null;
  created_at: string;
  agent: {
    name: string;
    description: string;
    image_url: string;
    category: string;
    slug: string;
  };
}

export interface CreatedAgent {
  id: string;
  name: string;
  slug: string;
  category: string;
  image_url: string;
  status: 'active' | 'pending' | 'rejected';
}

export interface AgentHistory {
  id: string;
  agent_id: string;
  total_interactions: number;
  total_tokens: number;
  total_duration_ms: number;
  last_interaction_at: string;
  first_interaction_at: string;
  agent: {
    name: string;
    description: string;
    image_url: string;
    category: string;
    slug: string;
  };
}

interface DashboardStats {
  total_interactions: number;
  total_duration_ms: number;
  most_used_agent: { name: string; count: number } | null;
  monthly_usage: { month: string; interactions: number }[];
}

interface ProfileState {
  settings: UserSettings | null;
  bookmarks: AgentBookmark[];
  history: AgentHistory[];
  createdAgents: CreatedAgent[];
  loading: boolean;
  error: string | null;
  initialized: boolean;
  dashboardStats: DashboardStats | null;
  
  // Settings
  fetchSettings: () => Promise<void>;
  updateSettings: (settings: Partial<UserSettings>) => Promise<void>;
  
  // Bookmarks
  fetchBookmarks: () => Promise<void>;
  addBookmark: (agentId: string, notes?: string) => Promise<void>;
  removeBookmark: (bookmarkId: string) => Promise<void>;
  updateBookmarkNotes: (bookmarkId: string, notes: string) => Promise<void>;
  
  // History
  fetchHistory: () => Promise<void>;
  clearHistory: () => Promise<void>;
  fetchDashboardData: () => Promise<void>;
  fetchCreatedAgents: (userId: string) => Promise<void>;
}

export const useProfileStore = create<ProfileState>((set, get) => ({
  settings: null,
  bookmarks: [],
  history: [],
  createdAgents: [],
  loading: false,
  error: null,
  initialized: false,
  dashboardStats: null,

  fetchSettings: async () => {
    if (get().initialized) return;
    
    try {
      set({ loading: true, error: null });
      
      const response = await axios.get('/api/profile/settings');
      set({ settings: response.data.settings, loading: false, initialized: true });
    } catch (error: any) {
      console.error('[Profile] Settings fetch failed:', error);
      set({ error: error.response?.data?.error?.message || error.message, loading: false, initialized: true });
    }
  },

  updateSettings: async (settings) => {
    try {
      set({ loading: true, error: null });
      
      const response = await axios.put('/api/profile/settings', settings);
      set({ settings: response.data.settings, loading: false });
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
      throw error;
    }
  },

  fetchBookmarks: async () => {
    try {
      set({ loading: true, error: null });
      
      const response = await axios.get('/api/profile/bookmarks');
      set({ bookmarks: response.data.bookmarks, loading: false });
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
    }
  },

  addBookmark: async (agentId, notes) => {
    try {
      set({ loading: true, error: null });
      
      const response = await axios.post('/api/profile/bookmarks', {
        agent_id: agentId,
        notes
      });
      
      set(state => ({
        bookmarks: [response.data.bookmark, ...state.bookmarks],
        loading: false
      }));
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
      throw error;
    }
  },

  removeBookmark: async (bookmarkId) => {
    try {
      set({ loading: true, error: null });
      
      await axios.delete(`/api/profile/bookmarks/${bookmarkId}`);
      
      set(state => ({
        bookmarks: state.bookmarks.filter(b => b.id !== bookmarkId),
        loading: false
      }));
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
      throw error;
    }
  },

  updateBookmarkNotes: async (bookmarkId, notes) => {
    try {
      set({ loading: true, error: null });
      
      const response = await axios.put(`/api/profile/bookmarks/${bookmarkId}`, { notes });
      
      set(state => ({
        bookmarks: state.bookmarks.map(b =>
          b.id === bookmarkId ? response.data.bookmark : b
        ),
        loading: false
      }));
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
      throw error;
    }
  },

  fetchHistory: async () => {
    try {
      set({ loading: true, error: null });
      
      const response = await axios.get('/api/profile/history');
      set({ history: response.data.history, loading: false });
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
    }
  },

  fetchCreatedAgents: async (userId: string) => {
    set({ loading: true, error: null });
    try {
      if (!userId) {
        throw new Error("User ID is required to fetch created agents.");
      }
      
      const response = await axios.get(`/api/profile/created-agents/${userId}`);
      set({ createdAgents: response.data.agents || [], loading: false });
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
    }
  },

  clearHistory: async () => {
    try {
      set({ loading: true, error: null });
      
      await axios.delete('/api/profile/history');
      set({ history: [], loading: false });
    } catch (error: any) {
      set({ error: error.response?.data?.error?.message || error.message, loading: false });
      throw error;
    }
  },

  fetchDashboardData: async () => {
    set({ loading: true });
    try {
      const response = await axios.get('/api/profile/dashboard-stats');
      
      set({
        dashboardStats: response.data.stats,
        loading: false,
        error: null,
      });
    } catch (error: any) {
      set({ 
        error: error.response?.data?.error?.message || `Failed to fetch dashboard data: ${error.message}`, 
        loading: false, 
        dashboardStats: null 
      });
    }
  },
}));