import { Connection, PublicKey, LAMPORTS_PER_SOL } from "@solana/web3.js";
import { getMint, TOKEN_PROGRAM_ID } from "@solana/spl-token";

const DGPU_MINT_ADDRESS_STRING = import.meta.env.VITE_DGPU_MINT_ADDRESS;

export async function getDGpuBalance(connection: Connection, walletAddress: PublicKey | null | undefined): Promise<number> {
  if (!walletAddress) {
    // If the wallet address is not provided, we can't fetch the balance.
    return 0;
  }
  
  if (!DGPU_MINT_ADDRESS_STRING) {
    console.error("CRITICAL: VITE_DGPU_MINT_ADDRESS is not set in your environment file. Cannot fetch dGPU balance. Please add it to your .env file.");
    return 0;
  }

  try {
    const DGPU_MINT_ADDRESS = new PublicKey(DGPU_MINT_ADDRESS_STRING);
    // First, get the mint info to find out the number of decimals.
    const mintInfo = await getMint(connection, DGPU_MINT_ADDRESS);
    const decimals = mintInfo.decimals;

    // Get all token accounts for the wallet
    const tokenAccounts = await connection.getParsedTokenAccountsByOwner(walletAddress, {
      programId: TOKEN_PROGRAM_ID,
    });

    // Find the token account for the dGPU mint address
    const dGpuAccount = tokenAccounts.value.find(
      (account) => account.account.data.parsed.info.mint === DGPU_MINT_ADDRESS.toBase58()
    );

    if (dGpuAccount && dGpuAccount.account.data.parsed.info.tokenAmount) {
      const balance = dGpuAccount.account.data.parsed.info.tokenAmount.uiAmount;
      return balance || 0;
    }

    return 0; // User does not have a dGPU token account
  } catch (error) {
    console.error("Failed to fetch dGPU balance:", error);
    // This can happen if the wallet is on a different network or other RPC issues.
    return 0;
  }
} 