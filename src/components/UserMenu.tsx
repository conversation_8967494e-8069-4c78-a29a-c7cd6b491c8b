import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { User, Settings, LogOut, LayoutDashboard } from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import Button from './Button';
import { ChevronDown } from 'lucide-react';

const UserMenu = () => {
  const { user, signOut } = useAuthStore();
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  if (!user) {
    return (
      <div className="flex items-center gap-4">
        <Link to="/signin">
          <Button variant="secondary">Sign In</Button>
        </Link>
        <Link to="/signup">
          <Button>Sign Up</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="relative" ref={menuRef}>
      <Button
        variant="ghost"
        onClick={() => setIsOpen(!isOpen)}
        className="p-2"
      >
        <User className="w-5 h-5" />
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-56 bg-gray-900/90 backdrop-blur-md border border-gray-700 rounded-lg shadow-lg py-1 z-50">
          <Link to="/dashboard" className="flex items-center gap-3 px-4 py-2 text-sm text-gray-300 hover:bg-gray-800/80 hover:text-white">
            <LayoutDashboard className="w-4 h-4" />
            Dashboard
          </Link>
          <Link to="/profile" className="flex items-center gap-3 px-4 py-2 text-sm text-gray-300 hover:bg-gray-800/80 hover:text-white">
            <User className="w-4 h-4" />
            Profile
          </Link>
          <Link to="/settings" className="flex items-center gap-3 px-4 py-2 text-sm text-gray-300 hover:bg-gray-800/80 hover:text-white">
            <Settings className="w-4 h-4" />
            Settings
          </Link>
          <div className="border-t border-gray-700/50 my-1"></div>
          <button
            onClick={() => {
              signOut();
              setIsOpen(false);
            }}
            className="w-full text-left flex items-center gap-3 px-4 py-2 text-sm text-red-400 hover:bg-red-500/20 hover:text-red-300"
          >
            <LogOut className="w-4 h-4" />
            Sign Out
          </button>
        </div>
      )}
    </div>
  );
};

export default UserMenu;