import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import Card from './Card';
import LoadingSpinner from './LoadingSpinner';

interface UsageData {
  month: string;
  interactions: number;
}

interface UsageChartProps {
  data: UsageData[] | undefined;
  loading: boolean;
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="p-4 bg-gray-900/90 border border-gray-700 rounded-lg shadow-lg">
        <p className="label text-white font-bold">{`${label}`}</p>
        <p className="intro text-cyan-400">{`Interactions : ${payload[0].value}`}</p>
      </div>
    );
  }

  return null;
};

const UsageChart: React.FC<UsageChartProps> = ({ data, loading }) => {
  return (
    <Card className="p-6 h-96" glass>
       <h3 className="font-bold text-white text-lg mb-4">Monthly Interactions (Last 6 Months)</h3>
      {loading ? (
        <div className="flex items-center justify-center h-full">
          <LoadingSpinner />
        </div>
      ) : (
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{
              top: 5,
              right: 20,
              left: -10,
              bottom: 40, 
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#4A5568" />
            <XAxis dataKey="month" stroke="#A0AEC0" angle={-45} textAnchor="end" height={60} />
            <YAxis stroke="#A0AEC0" />
            <Tooltip content={<CustomTooltip />} cursor={{ fill: 'rgba(100, 116, 139, 0.1)' }} />
            <Legend wrapperStyle={{ paddingTop: '20px' }}/>
            <Bar dataKey="interactions" fill="#38BDF8" name="Monthly Interactions" barSize={30} />
          </BarChart>
        </ResponsiveContainer>
      )}
    </Card>
  );
};

export default UsageChart; 