import React, { useState, useEffect } from 'react';
import { Search } from 'lucide-react';
import Input from './Input';

interface SearchBarProps {
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (query: string) => void;
  initialValue?: string;
  placeholder?: string;
  className?: string;
}

const SearchBar = ({
  value,
  onChange,
  onSearch,
  initialValue = '',
  placeholder = 'Search...',
  className = '',
}: SearchBarProps) => {
  const [searchValue, setSearchValue] = useState(initialValue);

  useEffect(() => {
    setSearchValue(initialValue);
  }, [initialValue]);

  const handleChange = (newValue: string) => {
    setSearchValue(newValue);
    onChange?.(newValue);
    onSearch?.(newValue);
  };

  return (
    <div className={`relative ${className}`}>
      <Input
        value={value !== undefined ? value : searchValue}
        onChange={(e) => handleChange(e.target.value)}
        placeholder={placeholder}
        icon={<Search className="w-5 h-5" />}
      />
    </div>
  );
};

export default SearchBar;