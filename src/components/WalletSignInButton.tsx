import React, { useState, useCallback, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useWalletModal } from '@solana/wallet-adapter-react-ui';
import { useAuthStore } from '../store/authStore';
import Button from './Button';
import { Wallet } from 'lucide-react';
import toast from 'react-hot-toast';
import '@solana/wallet-adapter-react-ui/styles.css';

const WalletSignInButton = () => {
  const { publicKey, signMessage, connect, wallet, connected } = useWallet();
  const { setVisible, visible } = useWalletModal();
  const { signInWithWallet, loading: authLoading } = useAuthStore();

  const [isAttemptingSignIn, setIsAttemptingSignIn] = useState(false);

  const performSignIn = useCallback(async () => {
    if (!publicKey || !signMessage) return;
    setIsAttemptingSignIn(false);
    await signInWithWallet(signMessage, publicKey);
  }, [publicKey, signMessage, signInWithWallet]);

  const handleSignInClick = useCallback(async () => {
    if (connected) {
      await performSignIn();
    } else {
      setIsAttemptingSignIn(true);
      if (wallet) {
        connect().catch(() => {
          setIsAttemptingSignIn(false);
        });
      } else {
        setVisible(true);
      }
    }
  }, [connected, wallet, connect, setVisible, performSignIn]);
  
  useEffect(() => {
    if (connected && isAttemptingSignIn) {
      performSignIn();
    }
  }, [connected, isAttemptingSignIn, performSignIn]);
  
  useEffect(() => {
    // This effect handles the case where the user closes the modal without selecting a wallet.
    if (!visible && !connected && isAttemptingSignIn) {
      setIsAttemptingSignIn(false);
    }
  }, [visible, connected, isAttemptingSignIn]);


  return (
    <Button
      variant="outline"
      icon={Wallet}
      onClick={handleSignInClick}
      loading={authLoading || isAttemptingSignIn}
      className="w-full"
    >
      Sign In with Solana
    </Button>
  );
};

export default WalletSignInButton; 