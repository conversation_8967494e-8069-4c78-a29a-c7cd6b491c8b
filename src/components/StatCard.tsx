import React from 'react';
import { LucideIcon } from 'lucide-react';
import Card from './Card'; // Assuming a base Card component exists
import LoadingSpinner from './LoadingSpinner';

interface StatCardProps {
  title: string;
  value: string | number | undefined;
  icon: LucideIcon;
  unit?: string;
  loading: boolean;
  className?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon: Icon, unit, loading, className }) => {
  return (
    <Card className={`p-6 flex flex-col justify-between ${className}`} glass>
      <div className="flex items-center justify-between text-gray-400">
        <span className="text-sm font-medium">{title}</span>
        <Icon className="w-6 h-6" />
      </div>
      <div className="mt-4">
        {loading ? (
          <div className="h-8 w-24 bg-gray-700/50 animate-pulse rounded-md"></div>
        ) : (
          <h3 className="text-3xl font-bold text-white">
            {value}
            {unit && <span className="text-lg font-medium text-gray-300 ml-2">{unit}</span>}
          </h3>
        )}
      </div>
    </Card>
  );
};

export default StatCard; 