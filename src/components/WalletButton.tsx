import React, { useEffect } from 'react';
import { useWallet, useConnection } from '@solana/wallet-adapter-react';
import { useWalletModal } from '@solana/wallet-adapter-react-ui';
import { useWalletStore } from '../store/walletStore';
import Button from './Button';
import { LogOut, Wallet } from 'lucide-react';

import '@solana/wallet-adapter-react-ui/styles.css';

const WalletButton: React.FC = () => {
  const { connected, publicKey, disconnect } = useWallet();
  const { setVisible } = useWalletModal();
  const {
    address,
    loading,
    connectWallet,
    disconnectWallet,
  } = useWalletStore();
  const { connection } = useConnection();

  const handleConnect = () => {
    setVisible(true);
  };

  const handleDisconnect = () => {
    disconnect().catch(() => {});
  };

  useEffect(() => {
    if (connected && publicKey && connection) {
      // Wallet adapter is connected, now connect our application state
      connectWallet(publicKey, connection);
    } else {
      // Wallet adapter is disconnected
      disconnectWallet();
    }
  }, [connected, publicKey, connection, connectWallet, disconnectWallet]);

  if (address) {
    return (
      <Button
        variant="outline"
        onClick={handleDisconnect}
        icon={LogOut}
        loading={loading}
      >
        {address.slice(0, 4)}...{address.slice(-4)}
      </Button>
    );
  }

  return (
    <Button onClick={handleConnect} icon={Wallet} loading={loading}>
      Connect Wallet
    </Button>
  );
};

export default WalletButton;