import axios from 'axios';

// API base configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || '/api';

// Create axios instance
export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear auth token and redirect to login
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  register: async (email: string, password: string, username: string) => {
    const response = await api.post('/auth/register', { email, password, username });
    return response.data;
  },

  logout: async () => {
    const response = await api.post('/auth/logout');
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
    return response.data;
  },

  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return response.data;
  },

  connectWallet: async (walletAddress: string, signature: string) => {
    const response = await api.post('/auth/connect-wallet', { 
      wallet_address: walletAddress, 
      signature 
    });
    return response.data;
  }
};

// Agents API
export const agentsAPI = {
  getAgents: async (filters?: any) => {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }
    const response = await api.get(`/agents/agents?${params.toString()}`);
    return response.data;
  },

  getFeaturedAgents: async () => {
    const response = await api.get('/agents/featured');
    return response.data;
  },

  getAgentBySlug: async (slug: string) => {
    const response = await api.get(`/agents/${slug}`);
    return response.data;
  },

  getCategories: async () => {
    const response = await api.get('/agents/categories');
    return response.data;
  },

  getRankings: async (sortBy?: string) => {
    const response = await api.get(`/agents/rankings?sortBy=${sortBy || 'rating'}`);
    return response.data;
  },

  createAgent: async (agentData: any) => {
    const response = await api.post('/agents/agents', agentData);
    return response.data;
  },

  updateAgent: async (agentId: string, updates: any) => {
    const response = await api.put(`/agents/agents/${agentId}`, updates);
    return response.data;
  }
};

// Chat API
export const chatAPI = {
  getMessages: async (agentId?: string) => {
    const params = agentId ? `?agentId=${agentId}` : '';
    const response = await api.get(`/chat/messages${params}`);
    return response.data;
  },

  sendMessage: async (content: string, agentId?: string, isMarkdown?: boolean) => {
    const response = await api.post('/chat/messages', {
      content,
      agent_id: agentId,
      is_markdown: isMarkdown
    });
    return response.data;
  },

  saveBotResponse: async (content: string, agentId?: string, isMarkdown?: boolean) => {
    const response = await api.post('/chat/messages/bot-response', {
      content,
      agent_id: agentId,
      is_markdown: isMarkdown
    });
    return response.data;
  },

  clearMessages: async () => {
    const response = await api.delete('/chat/messages');
    return response.data;
  }
};

// LLM API
export const llmAPI = {
  query: async (prompt: string, model: string, agentId?: string) => {
    const response = await api.post('/llm/query', {
      prompt,
      model,
      agent_id: agentId
    });
    return response.data;
  },

  getModels: async () => {
    const response = await api.get('/llm/models');
    return response.data;
  }
};

// Payment API
export const paymentAPI = {
  getBalance: async () => {
    const response = await api.get('/payment/balance');
    return response.data;
  },

  createPayment: async (agentId: string, amount: number, usageType?: string) => {
    const response = await api.post('/payment/create', {
      agent_id: agentId,
      amount,
      usage_type: usageType
    });
    return response.data;
  },

  confirmPayment: async (paymentId: string, transactionSignature: string) => {
    const response = await api.post('/payment/confirm', {
      payment_id: paymentId,
      transaction_signature: transactionSignature
    });
    return response.data;
  },

  getPaymentHistory: async () => {
    const response = await api.get('/payment/history');
    return response.data;
  },

  getUserCredits: async () => {
    const response = await api.get('/payment/credits');
    return response.data;
  }
};

// Profile API
export const profileAPI = {
  getProfile: async (username: string) => {
    const response = await api.get(`/profile/${username}`);
    return response.data;
  },

  updateProfile: async (updates: any) => {
    const response = await api.put('/profile', updates);
    return response.data;
  },

  getSettings: async () => {
    const response = await api.get('/profile/settings');
    return response.data;
  },

  updateSettings: async (settings: any) => {
    const response = await api.put('/profile/settings', settings);
    return response.data;
  }
};

export default api;
