import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import AgentCard from '../components/AgentCard';
import SearchBar from '../components/SearchBar';
import { useAgentStore } from '../store/agentStore';
import LoadingSpinner from '../components/LoadingSpinner';
import { useAuthStore } from '../store/authStore';

const Explore = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { agents, loading, error, fetchAgents } = useAgentStore();
  const authLoading = useAuthStore(state => state.loading);
  
  const [searchTerm, setSearchTerm] = useState(searchParams.get('search') || '');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');
  const [sortBy, setSortBy] = useState(searchParams.get('sortBy') || 'newest');
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    // Only fetch data when auth initialization is complete.
    if (!authLoading) {
      const filters = {
        search: searchTerm || undefined,
        category: selectedCategory || undefined,
        sortBy: sortBy || 'newest'
      };
      fetchAgents(filters);
    }
  }, [authLoading, searchTerm, selectedCategory, sortBy, fetchAgents]);

  useEffect(() => {
    // Update URL params when filters change
    const params = new URLSearchParams();
    if (searchTerm) params.set('search', searchTerm);
    if (selectedCategory) params.set('category', selectedCategory);
    if (sortBy !== 'newest') params.set('sortBy', sortBy);
    
    setSearchParams(params);
  }, [searchTerm, selectedCategory, sortBy, setSearchParams]);

  const handleSearch = (query: string) => {
    setSearchTerm(query);
  };

  const categories = [
    "AI Assistant",
    "Image Generation", 
    "Code Assistant",
    "Analysis",
    "Chat",
    "Writing",
    "Programming",
    "Research",
    "Knowledge",
    "Business",
    "Math",
    "Design",
    "Music"
  ];

  if (authLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[60vh]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Explore Agents</h1>
        <SearchBar onSearch={handleSearch} initialValue={searchTerm} />
      </div>

      {/* Filters */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-4 items-center">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="px-4 py-2 bg-gray-800 rounded-lg text-white hover:bg-gray-700 transition-colors"
          >
            Filters {showFilters ? '▲' : '▼'}
          </button>
          
          {showFilters && (
            <div className="flex flex-wrap gap-4 items-center">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white"
              >
                <option value="newest">Newest</option>
                <option value="oldest">Oldest</option>
                <option value="rating">Highest Rated</option>
                <option value="deployments">Most Popular</option>
                <option value="price_low">Price: Low to High</option>
                <option value="price_high">Price: High to Low</option>
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Results */}
      {loading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      ) : error ? (
        <div className="bg-red-500/10 border border-red-500 text-red-500 rounded-lg p-4">
          {error}
        </div>
      ) : agents.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-400 text-lg">No agents found matching your criteria.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {agents.map((agent) => (
            <AgentCard key={agent.id} agent={agent} />
          ))}
        </div>
      )}
    </div>
  );
};

export default Explore;