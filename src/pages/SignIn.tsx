import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Mail, Lock, AlertCircle } from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import { useGoogleAuth } from '../hooks/useGoogleAuth';
import Button from '../components/Button';
import Input from '../components/Input';
import GoogleButton from '../components/GoogleButton';
import WalletSignInButton from '../components/WalletSignInButton';

function SignIn() {
  const navigate = useNavigate();
  const { user, signIn, loading, error } = useAuthStore();
  const { signInWithGooglePopup, isGoogleLoaded } = useGoogleAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await signIn(email, password);
  };

  const handleGoogleSignIn = async () => {
    if (isGoogleLoaded) {
      signInWithGooglePopup();
    } else {
      console.error('Google Sign-In not ready');
    }
  };

  // Effect to redirect user if they are already logged in or when login is successful
  useEffect(() => {
    if (user) {
      navigate('/dashboard'); // Or to the intended page
    }
  }, [user, navigate]);

  return (
    <div className="min-h-[calc(100vh-73px)] flex items-center justify-center px-4">
      <div className="w-full max-w-md">
        <h1 className="text-3xl font-bold text-center mb-8">Sign In</h1>
        <div className="bg-gray-900/50 backdrop-blur-md border border-gray-800 rounded-xl p-8">
          {error && (
            <div className="flex items-center gap-2 bg-red-500/10 border border-red-500 text-red-500 rounded-lg p-4 mb-6">
              <AlertCircle className="w-5 h-5 shrink-0" />
              <p>{error}</p>
            </div>
          )}

          <div className="space-y-4">
            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                label="Email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                icon={<Mail className="w-5 h-5" />}
                placeholder="Enter your email"
                required
                glass
              />

              <Input
                label="Password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                icon={<Lock className="w-5 h-5" />}
                placeholder="Enter your password"
                required
                glass
              />

              <Button type="submit" loading={loading} className="w-full">
                Sign In
              </Button>
            </form>
            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-700" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-gray-900/50 px-2 text-gray-400">
                  Or continue with
                </span>
              </div>
            </div>
            <div className="space-y-3">
              <GoogleButton onClick={handleGoogleSignIn} loading={loading}>Sign In with Google</GoogleButton>
              <WalletSignInButton />
            </div>
          </div>
          
          <p className="mt-8 text-center text-sm text-gray-400">
            Don't have an account?{' '}
            <Link to="/signup" className="text-[#e1ffa6] hover:underline">
              Sign Up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

export default SignIn;