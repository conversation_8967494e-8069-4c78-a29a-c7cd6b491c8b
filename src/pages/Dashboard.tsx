import React, { useEffect } from 'react';
import { useProfileStore } from '../store/profileStore';
import { useAuthStore } from '../store/authStore';
import StatCard from '../components/StatCard';
import UsageChart from '../components/UsageChart';
import { Activity, Zap, Star, Clock, Bot } from 'lucide-react';
import LoadingSpinner from '../components/LoadingSpinner';
import Card from '../components/Card';
import { Link } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';

/**
 * Formats milliseconds into a human-readable string (e.g., 1h 23m 45s).
 * @param ms - The duration in milliseconds.
 * @returns A formatted string.
 */
function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`;
  }
  const totalSeconds = Math.floor(ms / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  const parts: string[] = [];
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (seconds > 0 || parts.length === 0) parts.push(`${seconds}s`);

  return parts.join(' ');
}

const Dashboard = () => {
  const { user, loading: authLoading } = useAuthStore();
  const { 
    dashboardStats, 
    fetchDashboardData, 
    loading: profileLoading, 
    history, 
    fetchHistory 
  } = useProfileStore();

  useEffect(() => {
    // Fetch data only once the user is authenticated.
    if (user) {
      if (!dashboardStats) fetchDashboardData();
      if (history.length === 0) fetchHistory();
    }
  }, [user, dashboardStats, history.length, fetchDashboardData, fetchHistory]);

  if (authLoading || (profileLoading && !dashboardStats)) {
    return (
      <div className="flex h-screen items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
      <header className="mb-12">
        <h1 className="text-4xl font-bold text-white tracking-tight">
          Welcome back, <span className="text-[#e1ffa6]">{user?.username || 'User'}</span>!
        </h1>
        <p className="mt-2 text-lg text-gray-400">Here's a summary of your activity on Agora.</p>
      </header>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        <StatCard
          title="Total Interactions"
          value={dashboardStats?.total_interactions}
          icon={Activity}
          loading={profileLoading}
        />
        <StatCard
          title="Total Usage"
          value={formatDuration(dashboardStats?.total_duration_ms || 0)}
          icon={Zap}
          loading={profileLoading}
        />
        <StatCard
          title="Favorite Agent"
          value={dashboardStats?.most_used_agent?.name || 'N/A'}
          icon={Star}
          loading={profileLoading}
          unit={dashboardStats?.most_used_agent ? `(${dashboardStats.most_used_agent.count} uses)`: ''}
        />
      </div>

      {/* Main Content Area */}
      <div className="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
            <UsageChart data={dashboardStats?.monthly_usage} loading={profileLoading} />
        </div>
        
        {/* Recent Activity */}
        <div className="lg:col-span-1">
            <h3 className="font-bold text-white text-lg mb-4">Recent Activity</h3>
            <Card glass className="p-4 max-h-96 overflow-y-auto">
                {profileLoading && history.length === 0 ? (
                    <div className="flex justify-center p-8">
                        <LoadingSpinner />
                    </div>
                ) : history.length > 0 ? (
                    <ul className="space-y-4">
                        {history.slice(0, 10).map(item => (
                            <li key={item.id}>
                                <Link to={`/agent/${item.agent.slug}`} className="block hover:bg-gray-800/50 p-3 rounded-lg transition-colors duration-200">
                                    <div className="flex items-center gap-4">
                                        <img src={item.agent.image_url} alt={item.agent.name} className="w-12 h-12 rounded-lg object-cover border-2 border-gray-700/50" />
                                        <div className="flex-1">
                                            <p className="font-semibold text-white truncate">{item.agent.name}</p>
                                            <p className="text-xs text-gray-400 flex items-center gap-1.5">
                                              <Bot className="w-3 h-3"/>
                                              {item.agent.category}
                                            </p>
                                        </div>
                                        <div className="text-xs text-gray-500 text-right flex flex-col items-end">
                                            <div className="flex items-center gap-1.5" title="Last interaction">
                                                <Clock className="w-3 h-3" />
                                                <span>{formatDistanceToNow(new Date(item.last_interaction_at), { addSuffix: true })}</span>
                                            </div>
                                             <div className="flex items-center gap-1.5 mt-1" title="Total interactions">
                                                <Activity className="w-3 h-3" />
                                                <span>{item.total_interactions}</span>
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            </li>
                        ))}
                    </ul>
                ) : (
                    <div className="flex flex-col items-center justify-center text-center py-12 text-gray-500">
                      <Bot className="w-12 h-12 mb-4"/>
                      <h4 className="font-semibold text-gray-300">No Activity Yet</h4>
                      <p className="text-sm">Interact with an agent to see your history here.</p>
                    </div>
                )}
            </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard; 