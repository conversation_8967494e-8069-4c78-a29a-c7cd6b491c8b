import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { useProfileStore } from '../store/profileStore';
import LoadingSpinner from '../components/LoadingSpinner';
import Card from '../components/Card';
import Button from '../components/Button';
import toast from 'react-hot-toast';
import { Settings, ShieldCheck, Clock, Bot, Activity } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import Badge from '../components/Badge';
import axios from 'axios';

// Define the structure for the profile data from user_profiles table
interface ProfileData {
  user_id: string;
  username: string;
  created_at: string;
  avatar_url?: string | null;
  bio?: string | null;
  website?: string | null;
  wallet_address?: string | null;
}

const ProfilePage: React.FC = () => {
  const { username: paramUsername } = useParams<{ username?: string }>();
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuthStore();
  const {
    history,
    createdAgents,
    fetchHistory,
    fetchCreatedAgents,
    loading: profileStoreLoading
  } = useProfileStore();

  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [pageLoading, setPageLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'created' | 'history'>('created');

  // Determine if the logged-in user is viewing their own profile
  const isOwnProfile = useMemo(() => {
    if (!user || !profile) return false;
    return user.id === profile.user_id;
  }, [user, profile]);

  useEffect(() => {
    const fetchProfile = async () => {
      if (authLoading) return;

      setPageLoading(true);
      setError(null);

      const targetUsername = paramUsername || user?.username;
      
      if (!targetUsername) {
        if (!authLoading) {
            toast.error("You must be logged in to view your profile.");
            navigate('/signin');
        }
        return;
      }

      try {
        const response = await axios.get(`/api/profile/profile/${targetUsername}`);
        setProfile(response.data.profile);
      } catch (err: any) {
        const errorMessage = err.response?.data?.error?.message || `Profile not found for user "${targetUsername}".`;
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setPageLoading(false);
      }
    };

    fetchProfile();
  }, [paramUsername, user, authLoading, navigate]);

  useEffect(() => {
    if (!profile) return;

    // Fetch data using the centralized store
    fetchCreatedAgents(profile.user_id);
    fetchHistory();
    
  }, [profile, fetchCreatedAgents, fetchHistory]);

  if (authLoading || pageLoading) {
    return <div className="flex h-[80vh] items-center justify-center"><LoadingSpinner size="lg" /></div>;
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto py-12 px-4 text-center">
        <Card glass>
            <h2 className="text-2xl font-bold text-red-400 mb-4">Error</h2>
            <p className="text-gray-300">{error}</p>
            <Button onClick={() => navigate('/')} className="mt-6">Go Home</Button>
        </Card>
      </div>
    );
  }
  
  if (!profile) {
    return <div className="flex h-[80vh] items-center justify-center">Profile could not be loaded.</div>;
  }

  const statusBadge = (status: string) => {
    switch (status) {
        case 'active': return <Badge variant="success">Active</Badge>;
        case 'pending': return <Badge variant="warning">Pending Review</Badge>;
        case 'rejected': return <Badge variant="error">Rejected</Badge>;
        default: return <Badge>{status}</Badge>;
    }
  };

  const renderCreatedAgents = () => {
    if (profileStoreLoading) return <div className="flex justify-center py-12"><LoadingSpinner /></div>;
    if (createdAgents.length === 0) return <p className="text-gray-400 text-center py-8">No agents created yet.</p>;

    return (
      <div className="space-y-4">
        {createdAgents.map(agent => (
          <Link to={`/agent/${agent.slug}`} key={agent.id}>
            <Card hover glass className="flex items-center gap-4">
              <img src={agent.image_url || `https://api.dicebear.com/8.x/bottts/svg?seed=${agent.name}`} alt={agent.name} className="w-16 h-16 rounded-lg object-cover"/>
              <div className="flex-1">
                <h3 className="font-bold">{agent.name}</h3>
                <p className="text-sm text-gray-400">{agent.category}</p>
              </div>
              {isOwnProfile && statusBadge(agent.status)}
            </Card>
          </Link>
        ))}
      </div>
    );
  };

  const renderInteractionHistory = () => {
    if (profileStoreLoading) return <div className="flex justify-center py-12"><LoadingSpinner /></div>;
    if (history.length === 0) return <p className="text-gray-400 text-center py-8">No interaction history yet.</p>;

    return (
      <div className="space-y-4">
        {history.map(item => (
          <Link to={`/agent/${item.agent.slug}`} key={item.agent_id}>
            <Card hover glass className="flex items-center gap-4">
              <img src={item.agent.image_url || `https://api.dicebear.com/8.x/bottts/svg?seed=${item.agent.name}`} alt={item.agent.name} className="w-16 h-16 rounded-lg object-cover"/>
              <div className="flex-1">
                <h3 className="font-bold">{item.agent.name}</h3>
                <p className="text-sm text-gray-400">{item.agent.category}</p>
              </div>
              <div className="text-right text-sm">
                  <div className="flex items-center gap-1 text-gray-300">
                    <Clock className="w-4 h-4"/>
                    <span>{formatDistanceToNow(new Date(item.last_interaction_at), { addSuffix: true })}</span>
                  </div>
                  <p className="text-gray-400 mt-1">{item.total_interactions} interactions</p>
              </div>
            </Card>
          </Link>
        ))}
      </div>
    );
  };


  return (
    <div className="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
       <Card glass>
        <div className="flex flex-col md:flex-row items-center gap-8">
          <div className="relative">
            <img
              src={profile.avatar_url || `https://api.dicebear.com/8.x/bottts/svg?seed=${profile.username}`}
              alt={`${profile.username}'s avatar`}
              className="w-32 h-32 rounded-full border-4 border-[#e1ffa6]/50 object-cover"
            />
          </div>
          <div className="text-center md:text-left">
            <h1 className="text-4xl font-bold text-white flex items-center gap-2">
              {profile.username}
              {isOwnProfile && (
                <span title="This is you">
                  <ShieldCheck className="w-6 h-6 text-cyan-400" />
                </span>
              )}
            </h1>
            <p className="mt-2 text-gray-400">
              Joined {formatDistanceToNow(new Date(profile.created_at), { addSuffix: true })}
            </p>
            {profile.bio && <p className="mt-4 text-gray-300 max-w-lg">{profile.bio}</p>}
             {profile.wallet_address && (
              <p className="mt-2 text-xs text-gray-500 font-mono" title={profile.wallet_address}>
                Wallet: {profile.wallet_address.slice(0, 6)}...{profile.wallet_address.slice(-4)}
              </p>
            )}
             {profile.website && (
                <a href={profile.website} target="_blank" rel="noopener noreferrer" className="text-cyan-400 hover:underline mt-2 inline-block">
                    {profile.website}
                </a>
            )}
          </div>
           {isOwnProfile && (
            <div className="md:ml-auto self-start">
                <Button onClick={() => navigate('/settings')} icon={Settings}>
                    Edit Profile
                </Button>
            </div>
           )}
        </div>
      </Card>
      
      <div className="mt-8">
        <div className="flex border-b border-gray-800">
          <button 
            onClick={() => setActiveTab('created')} 
            className={`px-4 py-2 font-medium transition-colors ${activeTab === 'created' ? 'border-b-2 border-[#e1ffa6] text-white' : 'text-gray-400 hover:text-white'}`}
          >
            <div className="flex items-center gap-2">
              <Bot className="w-4 h-4"/>
              <span>Created Agents</span>
              <span className="bg-gray-700 text-gray-300 text-xs rounded-full px-2 py-0.5">{createdAgents.length}</span>
            </div>
          </button>
          <button 
            onClick={() => setActiveTab('history')}
            className={`px-4 py-2 font-medium transition-colors ${activeTab === 'history' ? 'border-b-2 border-[#e1ffa6] text-white' : 'text-gray-400 hover:text-white'}`}
          >
            <div className="flex items-center gap-2">
              <Activity className="w-4 h-4"/>
              <span>Interaction History</span>
              <span className="bg-gray-700 text-gray-300 text-xs rounded-full px-2 py-0.5">{history.length}</span>
            </div>
          </button>
        </div>

        <div className="py-6">
          {activeTab === 'created' ? renderCreatedAgents() : renderInteractionHistory()}
        </div>
      </div>
    </div>
  );
};

export default ProfilePage; 