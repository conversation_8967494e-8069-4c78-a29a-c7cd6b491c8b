import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { User, Mail, Lock, AlertCircle } from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import { useGoogleAuth } from '../hooks/useGoogleAuth';
import Button from '../components/Button';
import Input from '../components/Input';
import GoogleButton from '../components/GoogleButton';
import WalletSignInButton from '../components/WalletSignInButton';

function SignUp() {
  const navigate = useNavigate();
  const { user, signUp, loading, error } = useAuthStore();
  const { signInWithGooglePopup, isGoogleLoaded } = useGoogleAuth();
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [formError, setFormError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError('');

    if (username.length < 3) {
      setFormError('Username must be at least 3 characters long.');
      return;
    }
    if (!/^[a-z0-9_-]+$/.test(username)) {
      setFormError('Username can only contain lowercase letters, numbers, underscores, and hyphens.');
      return;
    }
    if (password.length < 6) {
      setFormError('Password must be at least 6 characters long.');
      return;
    }

    await signUp(email, password, username);
  };

  const handleGoogleSignIn = async () => {
    if (isGoogleLoaded) {
      signInWithGooglePopup();
    } else {
      console.error('Google Sign-In not ready');
    }
  };

  useEffect(() => {
    if (user) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  return (
    <div className="min-h-[calc(100vh-73px)] flex items-center justify-center px-4">
      <div className="w-full max-w-md">
        <h1 className="text-3xl font-bold text-center mb-8">Create Account</h1>
        <div className="bg-gray-900/50 backdrop-blur-md border border-gray-800 rounded-xl p-8">
          {(formError || error) && (
            <div className="flex items-center gap-2 bg-red-500/10 border border-red-500 text-red-500 rounded-lg p-4 mb-6">
              <AlertCircle className="w-5 h-5 shrink-0" />
              <p>{formError || error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
                <Input
                  label="Username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value.toLowerCase())}
                  icon={<User className="w-5 h-5" />}
                  placeholder="Choose a username"
                  required
                  pattern="[a-z0-9_-]{3,30}"
                  title="3-30 characters. Letters, numbers, hyphens, and underscores only."
                  maxLength={30}
                  glass
                />
                 <p className="mt-2 text-xs text-gray-400">
                    3-30 characters, lowercase, no spaces.
                </p>
            </div>
            
            <Input
              label="Email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              icon={<Mail className="w-5 h-5" />}
              placeholder="Enter your email"
              required
              glass
            />

            <div>
              <Input
                label="Password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                icon={<Lock className="w-5 h-5" />}
                placeholder="Create a password (min. 6 characters)"
                required
                minLength={6}
                glass
              />
            </div>

            <Button 
              type="submit" 
              className="w-full !mt-6"
              loading={loading}
            >
              Create Account
            </Button>
          </form>

          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-gray-700" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-gray-900/50 px-2 text-gray-400">
                Or continue with
              </span>
            </div>
          </div>
          <div className="space-y-3">
            <GoogleButton onClick={handleGoogleSignIn} loading={loading}>Sign Up with Google</GoogleButton>
            <WalletSignInButton />
          </div>

          <p className="mt-8 text-center text-sm text-gray-400">
            Already have an account?{' '}
            <Link to="/signin" className="text-[#e1ffa6] hover:underline">
              Sign In
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

export default SignUp;