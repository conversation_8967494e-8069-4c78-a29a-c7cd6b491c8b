import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Bookmark, Pencil, Code, Brain, Briefcase, Calculator, Palette, Music } from 'lucide-react';
import { useAgentStore } from '../store/agentStore';
import LoadingSpinner from '../components/LoadingSpinner';
import Card from '../components/Card';
import { useAuthStore } from '../store/authStore';

interface CategoryCount {
  name: string;
  description: string;
  icon: string;
  count: number;
}

const categoryIcons = {
  "Writing": Pencil,
  "Programming": Code,
  "Research": Brain,
  "Knowledge": Bookmark,
  "Business": Briefcase,
  "Math": Calculator,
  "Design": Palette,
  "Music": Music,
  "AI Assistant": Brain,
  "Image Generation": Palette,
  "Code Assistant": Code,
  "Analysis": Calculator,
  "Chat": Brain
};

const categoryDescriptions = {
  "Writing": "Content creation, editing, and writing assistance",
  "Programming": "Code generation, debugging, and development tools",
  "Research": "Data analysis, academic research, and fact-checking",
  "Knowledge": "Information retrieval and learning assistance",
  "Business": "Business analysis, strategy, and planning",
  "Math": "Mathematical calculations and problem-solving",
  "Design": "Graphic design, UI/UX, and creative assistance",
  "Music": "Music composition, analysis, and theory",
  "AI Assistant": "General purpose AI assistants",
  "Image Generation": "AI tools for creating images",
  "Code Assistant": "Programming and development tools",
  "Analysis": "Data analysis and research tools",
  "Chat": "Conversational AI agents"
};

function Categories() {
  const { categories, loading, error, fetchCategories } = useAgentStore();
  const authLoading = useAuthStore(state => state.loading);

  useEffect(() => {
    // Only fetch data when auth initialization is complete.
    // This prevents a race condition on page load after OAuth redirects.
    if (!authLoading) {
      fetchCategories();
    }
  }, [authLoading, fetchCategories]);

  // Show a loading spinner if either the page data is loading or the auth state is being determined.
  if (loading || authLoading) {
    return (
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[60vh]">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="bg-red-500/10 border border-red-500 text-red-500 rounded-lg p-4">
          {error}
        </div>
      </div>
    );
  }

  // Combine API categories with fallback categories
  const categoryCounts = categories.map(category => ({
    name: category.name,
    description: categoryDescriptions[category.name as keyof typeof categoryDescriptions] || category.description,
    icon: category.name,
    count: category.count || 0
  })).sort((a, b) => b.count - a.count);

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Browse by Category</h1>
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        {categoryCounts.map((category) => {
          const Icon = categoryIcons[category.name as keyof typeof categoryIcons] || Brain;
          return (
            <Link 
              to={`/explore?category=${encodeURIComponent(category.name)}`}
              key={category.name}
              className="group"
            >
              <Card 
                hover 
                glow 
                glass 
                className="h-full relative overflow-hidden group-hover:border-[#e1ffa6]/50"
              >
                <div className="absolute -top-6 -right-6 w-24 h-24 bg-[#e1ffa6]/5 rounded-full blur-2xl group-hover:bg-[#e1ffa6]/10 transition-colors" />
                
                <div className="relative">
                  <div className="bg-[#e1ffa6]/10 w-12 h-12 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                    <Icon className="w-6 h-6 text-[#e1ffa6]" />
                  </div>
                  
                  <h3 className="text-xl font-bold mb-2 group-hover:text-[#e1ffa6] transition-colors">
                    {category.name}
                  </h3>
                  
                  <p className="text-gray-400 mb-4 line-clamp-2">
                    {category.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      {category.count} {category.count === 1 ? 'agent' : 'agents'}
                    </span>
                    <div className="w-8 h-8 rounded-full bg-gray-800/50 flex items-center justify-center group-hover:bg-[#e1ffa6]/10 transition-colors">
                      <Icon className="w-4 h-4 text-gray-400 group-hover:text-[#e1ffa6] transition-colors" />
                    </div>
                  </div>
                </div>
              </Card>
            </Link>
          );
        })}
      </div>
    </div>
  );
}

export default Categories;